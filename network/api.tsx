import AsyncStorage from "@react-native-async-storage/async-storage";
import axios from "axios";

export const api = axios.create({
  baseURL: "https://copdstageapi.azurewebsites.net",
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

api.interceptors.request.use(
  async (config) => {
    const token = await AsyncStorage.getItem("Token");
    console.log(token, "token in api");
    const UserDetails: any = await AsyncStorage.getItem("UserDetails");
    const parsedUserDetails = JSON.parse(UserDetails);

    if (token) {
      console.log(token, "token");
      config.headers.Authorization = `Bearer ${token}`;
    }
    if (parsedUserDetails?.ClientId) {
      config.headers.ClientId = parsedUserDetails.ClientId;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      // handle 401 error
    }
    return Promise.reject(error);
  }
);
