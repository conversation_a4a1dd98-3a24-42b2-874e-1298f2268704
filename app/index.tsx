import AsyncStorage from "@react-native-async-storage/async-storage";
import { router } from "expo-router";
import { useEffect } from "react";
import { Text, View } from "react-native";
import { checkVersion } from "react-native-check-version";

export default function Index() {
  useEffect(() => {
    const versionCheck = async () => {
      const version = await checkVersion();
      console.log("Got version info:", version);

      if (version.needsUpdate) {
        console.log(`App has a ${version.updateType} update pending.`);
      }
    };
    versionCheck();
  }, []);

  useEffect(() => {
    const checkAuth = async () => {
      const token = await AsyncStorage.getItem("Token");
      if (token) {
        router.replace("/(drawer)");
      } else {
        router.replace("/(auth)");
      }
    };
    setTimeout(() => {
      checkAuth();
    }, 2000);
  }, []);

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Text className="p-[20] color-gray-600 text-xl">Splash Screen</Text>
    </View>
  );
}
