import Loader from "@/components/modal/Loader";
import Header from "@/components/ui/Header";
import theme from "@/theme";
import { Stack } from "expo-router";
import { KeyboardAvoidingView, Platform, StatusBar } from "react-native";
import { PaperProvider } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import "../global.css";

export default function RootLayout() {
  const insets = useSafeAreaInsets();

  return (
    <KeyboardAvoidingView
      keyboardVerticalOffset={Platform.OS === "ios" ? 0 : -insets.top * 2}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <PaperProvider theme={theme}>
        <StatusBar barStyle="dark-content" />
        <Stack
          screenOptions={{
            headerShown: false,
            animation: "slide_from_right",
            headerShadowVisible: false,
          }}
        >
          <Stack.Screen
            name="(drawer)"
            options={{ title: "Home", headerShown: false }}
          />
          <Stack.Screen
            name="(auth)"
            options={{ title: "Authentication", headerShown: false }}
          />
          <Stack.Screen
            name="profile"
            options={{
              title: "Back",
              headerShown: true,
              header: (props) => (
                <Header title="Profile" showBackButton={true} {...props} />
              ),
            }}
          />
        </Stack>
        <Loader />
      </PaperProvider>
    </KeyboardAvoidingView>
  );
}
