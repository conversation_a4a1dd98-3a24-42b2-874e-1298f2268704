import Loader from "@/components/modal/Loader";
import Header from "@/components/ui/Header";
import theme from "@/theme";
import { useFonts } from "expo-font";
import { SplashScreen, Stack } from "expo-router";
import { useEffect } from "react";
import { KeyboardAvoidingView, Platform, StatusBar } from "react-native";
import { PaperProvider } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import "../global.css";

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    "Nunito-Regular": require("../assets/fonts/Nunito-Regular.ttf"), //400
    "Nunito-Medium": require("../assets/fonts/Nunito-Medium.ttf"), //500
    "Nunito-SemiBold": require("../assets/fonts/Nunito-SemiBold.ttf"), //600
    "Nunito-Bold": require("../assets/fonts/Nunito-Bold.ttf"), //700
  });
  const insets = useSafeAreaInsets();

  useEffect(() => {
    if (loaded || error) {
      console.log("Fonts loaded", loaded);

      SplashScreen.hideAsync();
    }
  }, [loaded, error]);

  if (!loaded || error) {
    return null;
  }

  return (
    <KeyboardAvoidingView
      keyboardVerticalOffset={Platform.OS === "ios" ? 0 : -insets.top * 2}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <PaperProvider theme={theme}>
        <StatusBar barStyle="dark-content" />
        <Stack
          screenOptions={{
            headerShown: false,
            animation: "slide_from_right",
            headerShadowVisible: false,
          }}
        >
          <Stack.Screen
            name="(drawer)"
            options={{
              title: "Home",
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="(auth)"
            options={{ title: "Authentication", headerShown: false }}
          />
          <Stack.Screen
            name="profile"
            options={{
              title: "Back",
              headerShown: true,
              header: (props) => (
                <Header title="Profile" showBackButton={true} {...props} />
              ),
            }}
          />
        </Stack>
        <Loader />
      </PaperProvider>
    </KeyboardAvoidingView>
  );
}
