import { COLORS, SCREEN_PADDING } from "@/constants";
import { api } from "@/network/api";
import { LOGIN } from "@/network/apiConstants";
import { useLoaderStore } from "@/store/loader.store";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { router } from "expo-router";
import React from "react";
import {
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
} from "react-native";
import { TouchableRipple } from "react-native-paper";

const LoginFormInput = ({
  label,
  placeholder,
}: {
  label: string;
  placeholder: string;
}) => {
  return (
    <View
      style={{
        gap: 4,
      }}
    >
      <Text
        style={{ color: "#797b7c", fontFamily: "Nunito-Medium", fontSize: 14 }}
      >
        {label}
      </Text>
      <TextInput
        placeholder={placeholder}
        placeholderTextColor={COLORS.black}
        style={{
          borderBottomWidth: 1,
          borderBottomColor: COLORS.gray,
          paddingVertical: 8,
          color: "#202325",
          fontFamily: "Nunito-Medium",
          fontSize: 16,
        }}
      />
    </View>
  );
};

async function saveUserData(data: any) {
  console.log(data);

  try {
    if (data.Token) {
      await AsyncStorage.setItem("Token", data.Token);
    }
    if (data.CreatedBy) {
      await AsyncStorage.setItem("CreatedBy", data.CreatedBy);
    }
    if (data.CreatedDate) {
      await AsyncStorage.setItem("CreatedDate", data.CreatedDate);
    }
    if (data.IsAuthenticated) {
      await AsyncStorage.setItem(
        "IsAuthenticated",
        data.IsAuthenticated.toString()
      );
    }
    if (data.IsClientSubscriptionExpired) {
      await AsyncStorage.setItem(
        "IsClientSubscriptionExpired",
        data.IsClientSubscriptionExpired
      );
    }
    if (data.IsLocked) {
      await AsyncStorage.setItem("IsLocked", data.IsLocked.toString());
    }
    if (data.IsTryToAccessAfterLock) {
      await AsyncStorage.setItem(
        "IsTryToAccessAfterLock",
        data.IsTryToAccessAfterLock.toString()
      );
    }
    if (data.NumberOfInvalidTries) {
      await AsyncStorage.setItem(
        "NumberOfInvalidTries",
        data.NumberOfInvalidTries
      );
    }
    if (data.Token) {
      await AsyncStorage.setItem("Token", data.Token);
    }
    if (data.UpdatedBy) {
      await AsyncStorage.setItem("UpdatedBy", data.UpdatedBy);
    }
    if (data.UpdatedDate) {
      await AsyncStorage.setItem("UpdatedDate", data.UpdatedDate);
    }

    //user details

    if (data.UserDetails) {
      await AsyncStorage.setItem(
        "UserDetails",
        JSON.stringify(data.UserDetails)
      );
    }
  } catch (error) {
    console.log(error);
  }
}

const Signin = () => {
  const setIsLoaderVisible = useLoaderStore(
    (state) => state.setIsLoaderVisible
  );

  const handleLogin = async () => {
    setIsLoaderVisible(true);
    try {
      const res = await api.post(LOGIN, {
        UserName: "<EMAIL>",
        Pwd: "Copd@123",
      });
      await saveUserData(res.data.Data);
      router.replace("/(drawer)");
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoaderVisible(false);
    }
  };

  return (
    <View
      style={{
        flex: 1,
      }}
    >
      <ScrollView
        contentContainerStyle={{
          padding: SCREEN_PADDING * 2,
          gap: 24,
          justifyContent: "center",
          flexGrow: 1,
          paddingTop: 0,
          backgroundColor: COLORS.white,
        }}
      >
        <Text style={{ fontFamily: "Nunito-Bold", fontSize: 32 }}>Login</Text>

        <LoginFormInput label="Email Address" placeholder="Enter email" />
        <LoginFormInput label="Password" placeholder="Enter password" />

        <Pressable
          style={{}}
          onPress={() => {
            console.log("Forgot password?");
          }}
        >
          <Text
            style={{
              color: COLORS.blue,
              fontFamily: "Nunito-Medium",
              fontSize: 16,
            }}
          >
            Forgot password?
          </Text>
        </Pressable>
        <TouchableRipple
          rippleColor="rgba(255, 255, 255, 0.1)"
          style={{
            backgroundColor: COLORS.blue,
            padding: 14,
            borderRadius: 90,
            marginTop: 8,
          }}
          onPress={handleLogin}
        >
          <Text
            style={{
              color: COLORS.white,
              textAlign: "center",
              fontFamily: "Nunito-Medium",
              fontSize: 16,
            }}
          >
            Login
          </Text>
        </TouchableRipple>
      </ScrollView>
    </View>
  );
};

export default Signin;

const styles = StyleSheet.create({});
