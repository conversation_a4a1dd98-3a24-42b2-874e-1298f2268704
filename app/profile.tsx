import MyProfileActionButton from "@/components/ui/MyProfileActionButton";
import ScreenLabel from "@/components/ui/ScreenLabel";
import { SCREEN_PADDING } from "@/constants";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { router } from "expo-router";
import React, { useEffect } from "react";
import { Image, ScrollView, StyleSheet, TextInput, View } from "react-native";
import { Button, Text } from "react-native-paper";

const ProfileFormInputItem = ({
  label,
  value,
}: {
  label: string;
  value: string;
}) => {
  return (
    <View
      style={{
        gap: 4,
      }}
    >
      <Text variant="bodyLarge" style={{ color: "#797b7c" }}>
        {label}
      </Text>
      <TextInput
        value={value}
        style={{
          borderBottomWidth: 1,
          borderBottomColor: "#202325",
          paddingVertical: 8,
          color: "#202325",
          fontFamily: "Poppins",
          fontSize: 16,
        }}
      />
    </View>
  );
};

const Profile = () => {
  const handleLogout = async () => {
    try {
      await AsyncStorage.clear();
      router.replace("/(auth)");
    } catch (error) {
      console.log("can not logout ", error);
    }
  };

  useEffect(() => {
    return () => {
      console.log("unmounting profile");
    };
  }, []);

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      keyboardDismissMode="none"
      style={{ flex: 1, backgroundColor: "#fff" }}
      contentContainerStyle={{
        padding: SCREEN_PADDING * 2,
        // flexGrow: 1,
        paddingTop: 0,
        backgroundColor: "#fff",
      }}
    >
      <ScreenLabel label="User Profile" />
      <View style={{ flex: 1, alignItems: "center" }}>
        <Button
          mode="text"
          labelStyle={{
            color: "#306CFE",
            fontWeight: "600",
            fontSize: 16,
            textAlign: "right",
          }}
          onPress={() => {}}
          style={{ marginVertical: 20, alignSelf: "flex-end" }}
        >
          Save Changes
        </Button>

        <View style={styles.profileContainer}>
          <Image
            resizeMode="contain"
            source={require("../assets/images/png/user.png")}
            style={{
              width: 110,
              height: 110,
            }}
          />
        </View>
        <View style={styles.formContainer}>
          <ProfileFormInputItem label="Name" value="John Doe" />
          <ProfileFormInputItem label="Phone" value="1234567890" />
          <ProfileFormInputItem label="Email" value="<EMAIL>" />
        </View>
        <MyProfileActionButton
          label="Change Image"
          icon="image-outline"
          color="#0A0A0A"
          onPress={() => {}}
        />
        <MyProfileActionButton
          label="Reset Password"
          icon="refresh"
          color="#0A0A0A"
          onPress={() => {}}
        />
        <MyProfileActionButton
          label="Logout"
          icon="logout"
          color="#D2042D"
          onPress={handleLogout}
        />
      </View>
      {/* </View> */}
    </ScrollView>
  );
};

export default Profile;

const styles = StyleSheet.create({
  profileContainer: {
    borderRadius: 999,
    padding: 16,
    overflow: "hidden",
    backgroundColor: "#0D66D00A",
    justifyContent: "center",
    alignItems: "center",
  },
  formContainer: {
    gap: 20,
    width: "100%",
    backgroundColor: "#0D66D00A",
    borderRadius: 14,
    marginTop: 20,
    padding: 16,
    marginBottom: 16,
  },
});
