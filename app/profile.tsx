import MyProfileActionButton from "@/components/ui/MyProfileActionButton";
import { SCREEN_PADDING } from "@/constants";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { router } from "expo-router";
import React from "react";
import { Image, Pressable, StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";

const Profile = () => {
  const handleLogout = async () => {
    // Handle logout logic here
    console.log("User logged out");
    await AsyncStorage.clear();
    router.replace("/(auth)");
  };
  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <View
        style={{
          height: "50%",
          width: "100%",
          backgroundColor: "#F0F4F9",
          justifyContent: "center",
          alignItems: "center",
          paddingHorizontal: SCREEN_PADDING,
        }}
      >
        <Pressable
          onPress={() => console.log("Profile Pressed")}
          style={styles.profileContainer}
        >
          <Image
            source={require("../assets/images/png/user.png")}
            style={styles.profile}
          />
        </Pressable>
        <Text variant="titleMedium">John Doe</Text>
        <Text variant="titleSmall"><EMAIL></Text>
      </View>
      <View
        style={{
          height: "50%",
          width: "100%",
          backgroundColor: "#FFF",
          paddingHorizontal: SCREEN_PADDING,
        }}
      >
        <MyProfileActionButton
          icon="lock-outline"
          color="#222"
          label="Change Password"
          onPress={() => console.log("Change Password Pressed")}
        />
        <MyProfileActionButton
          icon="image-edit-outline"
          color="#222"
          label="Change Image"
          onPress={() => console.log("Change Image Pressed")}
        />
        <MyProfileActionButton
          icon="logout-variant"
          color="#222"
          label="Logout"
          onPress={handleLogout}
        />
      </View>
    </View>
  );
};

export default Profile;

const styles = StyleSheet.create({
  profile: {
    width: 60,
    height: 60,
    alignSelf: "center",
  },
  profileContainer: {
    width: 100,
    height: 100,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
    borderRadius: 50,
    backgroundColor: "#fff",
    marginBottom: 20,
  },
  profileText: {
    fontSize: 18,
    fontWeight: "bold",
  },
});
