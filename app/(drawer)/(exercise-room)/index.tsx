import ScreenLabel from "@/components/ui/ScreenLabel";
import { SCREEN_PADDING } from "@/constants";
import React from "react";
import { Pressable, StyleSheet, View } from "react-native";
import { Icon, Text } from "react-native-paper";

const EXERCISE_ROOM_CARDS = [
  {
    id: 1,
    label: "My Steps",
    linkTo: "(drawer)/(exercise-room)",
    description:
      "Check the number of steps you have taken by tapping the icon.",
  },
  {
    id: 2,
    label: "Why Should I Exercise",
    linkTo: "(drawer)/(exercise-room)",
    description: "Learn about the benifits of exercise by tapping the icon.",
  },
  {
    id: 3,
    label: "Breathless during exercise",
    linkTo: "(drawer)/(exercise-room)",
    description: "What do I do if I get breathless whilist exercising?",
  },
];

const Index = () => {
  return (
    <View
      style={{
        flex: 1,
        padding: SCREEN_PADDING * 2,
        backgroundColor: "#fff",
        paddingTop: 0,
      }}
    >
      <ScreenLabel label="My Exercise Room" style={{ marginBottom: 20 }} />
      {EXERCISE_ROOM_CARDS.map((item) => (
        <View
          key={item.id}
          style={{
            marginBottom: 16,
            padding: 20,
            borderRadius: 14,
            backgroundColor: "#0D66D00A",
            flexDirection: "row",
            alignItems: "center",
            gap: 10,
          }}
        >
          <View style={{ flex: 1 }}>
            <Text variant="titleLarge" style={{ color: "#373333" }}>
              {item.label}
            </Text>
            <Text style={{ color: "#797b7c" }} variant="bodyLarge">
              {item.description}
            </Text>
          </View>
          <View style={{}}>
            <Pressable onPress={() => console.log("View Details")}>
              <Icon source={"arrow-right"} size={24} color="#0D66D0" />
            </Pressable>
          </View>
        </View>
      ))}
    </View>
  );
};

export default Index;

const styles = StyleSheet.create({});
