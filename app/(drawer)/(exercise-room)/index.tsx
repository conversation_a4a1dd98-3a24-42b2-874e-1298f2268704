import LabelUnderline from "@/components/ui/LabelUnderline";
import <PERSON><PERSON>abel from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import React from "react";
import { Pressable, StyleSheet, Text, View } from "react-native";
import { Icon } from "react-native-paper";

const EXERCISE_ROOM_CARDS = [
  {
    id: 1,
    label: "My Steps",
    linkTo: "(drawer)/(exercise-room)",
    description:
      "Check the number of steps you have taken by tapping the icon.",
  },
  {
    id: 2,
    label: "Why Should I Exercise",
    linkTo: "(drawer)/(exercise-room)",
    description: "Learn about the benifits of exercise by tapping the icon.",
  },
  {
    id: 3,
    label: "Breathless during exercise",
    linkTo: "(drawer)/(exercise-room)",
    description: "What do I do if I get breathless whilist exercising?",
  },
];

const PR_CARDS = [
  {
    id: 1,
    label: "Progress Report 1",
    linkTo: "(drawer)/(exercise-room)",
    description: "What is PR and how to get it?",
  },
  {
    id: 2,
    label: "Progress Report 2",
    linkTo: "(drawer)/(exercise-room)",
    description: "Is PR good for me?",
  },
];

const Index = () => {
  return (
    <View
      style={{
        flex: 1,
        padding: SCREEN_PADDING * 2,
        backgroundColor: "#fff",
        paddingTop: 0,
      }}
    >
      <ScreenLabel label="My Exercise Room" style={{ marginBottom: 20 }} />
      {EXERCISE_ROOM_CARDS.map((item) => (
        <View
          key={item.id}
          style={{
            marginBottom: 16,
            padding: 20,
            borderRadius: 14,
            backgroundColor: "#0D66D00A",
            flexDirection: "row",
            alignItems: "center",
            gap: 10,
          }}
        >
          <View style={{ flex: 1, gap: 8 }}>
            <Text
              style={{
                color: "#373333",
                fontFamily: "Nunito-Medium",
                fontSize: 22,
              }}
            >
              {item.label}
            </Text>
            <Text
              style={{
                color: "#797b7c",
                fontFamily: "Nunito-Medium",
                fontSize: 14,
                lineHeight: 16,
              }}
            >
              {item.description}
            </Text>
          </View>
          <View style={{}}>
            <Pressable onPress={() => console.log("View Details")}>
              <Icon source={"arrow-right"} size={24} color="#0D66D0" />
            </Pressable>
          </View>
        </View>
      ))}
      {/* <Text
        style={{
          color: COLORS.black,
          fontFamily: "Nunito-Regular",
          fontSize: 18,
          marginTop: 8,
          borderBottomWidth: 1,
          borderBottomColor: COLORS.gray,
          marginBottom: 16,
          padding: 8,
        }}
      >
        Pulmonary Rehabilitation (PR)
      </Text> */}
      <LabelUnderline
        label="Pulmonary Rehabilitation (PR)"
        style={{ fontFamily: "Nunito-Regular" }}
      />

      {PR_CARDS.map((item) => (
        <View
          key={item.id}
          style={{
            marginBottom: 16,
            padding: 20,
            borderRadius: 14,
            backgroundColor: "#0D66D00A",
            flexDirection: "row",
            alignItems: "center",
            gap: 10,
          }}
        >
          <View style={{ flex: 1, gap: 8 }}>
            <Text
              style={{
                color: COLORS.black,
                fontFamily: "Nunito-SemiBold",
                fontSize: 16,
                lineHeight: 20,
              }}
            >
              {item.description}
            </Text>
          </View>
          <View style={{}}>
            <Pressable onPress={() => console.log("View Details")}>
              <Icon source={"arrow-right"} size={24} color="#0D66D0" />
            </Pressable>
          </View>
        </View>
      ))}
    </View>
  );
};

export default Index;

const styles = StyleSheet.create({});
