import { DrawerToggleButton } from "@react-navigation/drawer";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _AboutLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          headerLeft: () => <DrawerToggleButton />,
          title: "About COPD Insight",
        }}
      />
    </Stack>
  );
};

export default _AboutLayout;

const styles = StyleSheet.create({});
