import Header from "@/components/ui/Header";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _AboutLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
        header: (props) => <Header title="About COPD Insight" {...props} />,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          // headerLeft: () => <DrawerToggleButton />,
          title: "About COPD Insight",
        }}
      />
    </Stack>
  );
};

export default _AboutLayout;

const styles = StyleSheet.create({});
