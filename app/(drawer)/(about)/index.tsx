import ScreenLabel from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import { ABOUT_COPD_INSIGHT_CONTENT } from "@/data";
import React from "react";
import { Pressable, ScrollView, Text, View } from "react-native";
import { Icon } from "react-native-paper";

const CollapsableAboutItem = ({
  item,
  isOpen,
  onSelect,
}: {
  item: {
    id: number;
    label: string;
    description: string;
    website: string;
    email: string;
  };
  isOpen: boolean;
  onSelect: (id: number) => void;
}) => {
  return (
    <View
      key={item.id}
      style={{
        backgroundColor: COLORS.cardBg,
        borderRadius: 14,
        padding: 16,
        marginBottom: 16,
      }}
    >
      <Pressable
        onPress={() => onSelect(item.id)}
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Text
          style={{
            color: COLORS.black,
            fontFamily: "Nunito-SemiBold",
            fontSize: 16,
          }}
        >
          {item.label}
        </Text>
        <Icon
          source={isOpen ? "chevron-up" : "chevron-down"}
          size={24}
          color={COLORS.black}
        />
      </Pressable>

      {isOpen && (
        <Text
          style={{
            color: COLORS.gray,
            fontFamily: "Nunito-Medium",
            fontSize: 14,
            lineHeight: 16,
            marginTop: 8,
          }}
        >
          {item.description}
        </Text>
      )}
    </View>
  );
};

const About = () => {
  const [selectedItem, setSelectedItem] = React.useState<number | null>(null);
  const handleSelectedItem = (id: number) => {
    setSelectedItem(id === selectedItem ? null : id);
  };

  return (
    <View
      style={{
        flex: 1,
        padding: SCREEN_PADDING * 2,
        backgroundColor: COLORS.white,
      }}
    >
      <ScrollView style={{ flex: 1 }}>
        <ScreenLabel label="About COPD Insight" style={{ marginBottom: 20 }} />
        {ABOUT_COPD_INSIGHT_CONTENT.map((item) => (
          <CollapsableAboutItem
            item={item}
            key={item.id}
            onSelect={handleSelectedItem}
            isOpen={selectedItem === item.id}
          />
        ))}
      </ScrollView>
    </View>
  );
};

export default About;
