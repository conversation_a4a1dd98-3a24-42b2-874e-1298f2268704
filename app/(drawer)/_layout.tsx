// app/(drawer)/_layout.tsx
import {
  DrawerContentComponentProps,
  DrawerContentScrollView,
  DrawerItemList,
} from "@react-navigation/drawer";
import { Link } from "expo-router";
import { Drawer } from "expo-router/drawer";
import { Image, View } from "react-native";
import { Divider, Icon, Text, TouchableRipple } from "react-native-paper";

const CustomDrawerContent = (props: DrawerContentComponentProps) => {
  return (
    <DrawerContentScrollView {...props}>
      <View
        style={{
          alignItems: "center",
        }}
      >
        <Link href="/profile" asChild>
          <TouchableRipple
            onPress={() => {
              // Handle profile picture press
              console.log("Profile picture pressed");
            }}
            // rippleColor={"rgba(0, 0, 0, .32)"}
          >
            <>
              <View
                style={{
                  borderRadius: 999,
                  overflow: "hidden",
                  backgroundColor: "#e0e0e0",
                  width: 88,
                  height: 88,
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Image
                  source={require("../../assets/images/png/user.png")}
                  style={{ width: 62, height: 62 }}
                />
              </View>
              <Text variant="bodyLarge">Welcome, User</Text>
            </>
          </TouchableRipple>
        </Link>
      </View>
      <Divider style={{ marginVertical: 8 }} />
      <DrawerItemList {...props} />
    </DrawerContentScrollView>
  );
};

export default function DrawerLayout() {
  return (
    <Drawer
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        drawerStyle: { backgroundColor: "#f5f5f5" },
        drawerStatusBarAnimation: "fade",
        drawerActiveTintColor: "#111",
        headerShadowVisible: false,
        drawerItemStyle: {
          marginVertical: 0,
          borderRadius: 4,
          padding: 0,
        },
        // headerLeft: () => <DrawerToggleButton />, // Default burger menu
      }}
    >
      <Drawer.Screen
        name="(dashboard)"
        options={{
          title: "Dashboard",
          headerShown: false, // Hide header here, let stack handle it
          drawerIcon: ({ color }) => (
            <Icon source="view-dashboard-outline" size={24} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="(assessments)"
        options={{
          title: "My Assessments",
          headerShown: false, // Hide header here, let stack handle it
          drawerIcon: ({ color }) => (
            <Icon source="clipboard-list-outline" size={24} color={color} />
          ),
        }}
      />

      <Drawer.Screen
        name="(clinic-history)"
        options={{
          title: "My Clinic History",
          headerShown: false,
          drawerIcon: ({ color }) => (
            <Icon source="history" size={24} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="(progress-reports)"
        options={{
          title: "My Progress Reports",
          headerShown: false,
          drawerIcon: ({ color }) => (
            <Icon source="file-document-outline" size={24} color={color} />
          ),
        }}
      />

      <Drawer.Screen
        name="(staying-healthy)"
        options={{
          title: "My Staying Healthy",
          headerShown: false,
          drawerIcon: ({ color }) => (
            <Icon source="heart-plus-outline" size={24} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="(my-rewards)"
        options={{
          title: "My Rewards",
          headerShown: false,
          drawerIcon: ({ color }) => (
            <Icon source="gift-outline" size={24} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="(exercise-room)"
        options={{
          title: "My Exercise Room",
          headerShown: false,
          drawerIcon: ({ color }) => (
            <Icon source="run" size={24} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="(my-appointments)"
        options={{
          title: "My Appointments",
          headerShown: false,
          drawerIcon: ({ color }) => (
            <Icon source="calendar-clock" size={24} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="(my-messages)"
        options={{
          title: "My Messages",
          headerShown: false,
          drawerIcon: ({ color }) => (
            <Icon source="email-outline" size={24} color={color} />
          ),
        }}
      />

      <Drawer.Screen
        name="(about)"
        options={{
          title: "About COPD Insight",
          headerShown: false,
          drawerIcon: ({ color }) => (
            <Icon source="information-outline" size={24} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="(terms-and-conditions)"
        options={{
          title: "Terms and Conditions",
          headerShown: false,
          drawerIcon: ({ color }) => (
            <Icon source="text-box-check-outline" size={24} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="(faqs)"
        options={{
          title: "FAQs",
          headerShown: false,
          drawerIcon: ({ color }) => (
            <Icon source="frequently-asked-questions" size={24} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="(medical-references)"
        options={{
          title: "Medical References",
          headerShown: false,
          drawerIcon: ({ color }) => (
            <Icon source="medical-bag" size={24} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="(settings)"
        options={{
          title: "Settings",
          headerShown: false,
          drawerIcon: ({ color }) => (
            <Icon source="cog-outline" size={24} color={color} />
          ),
        }}
      />
    </Drawer>
  );
}
