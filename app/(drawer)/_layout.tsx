// app/(drawer)/_layout.tsx
import Header from "@/components/ui/Header";
import { COLORS } from "@/constants";
import {
  DrawerContentComponentProps,
  DrawerContentScrollView,
  DrawerItem,
  DrawerItemList,
} from "@react-navigation/drawer";
import { Link, useNavigation } from "expo-router";
import { Drawer } from "expo-router/drawer";
import React from "react";
import { Image, StatusBar, Text, View } from "react-native";
import { Button, Divider, Icon, TouchableRipple } from "react-native-paper";

const DRAWER_ITEM_FONT_SIZE = 20;

const CustomDrawerContent = (props: DrawerContentComponentProps) => {
  const [showSettings, setShowSettings] = React.useState(false);

  return (
    <DrawerContentScrollView {...props}>
      <View>
        <Button
          onPress={() => props.navigation.closeDrawer()}
          style={{
            // backgroundColor: "#f0f0f0",
            borderRadius: 99,
            alignSelf: "flex-end",
          }}
        >
          <Icon source="close" size={24} color="#0A0A0A" />
        </Button>
      </View>
      <Link href="/profile" asChild>
        <TouchableRipple
          style={{
            flexDirection: "row",
            alignItems: "center",
            gap: 16,
            // padding: 10,
            paddingLeft: 10,
            marginBottom: 10,
            borderRadius: 99,
          }}
        >
          <>
            <View
              style={{
                borderRadius: 24,
                overflow: "hidden",
                backgroundColor: "#f0f0f0",
              }}
            >
              <Image
                resizeMode="contain"
                source={require("../../assets/images/png/user.png")}
                style={{ width: 48, height: 48 }}
              />
            </View>
            <Text
              style={{
                color: COLORS.black,
                fontFamily: "Nunito-Medium",
                fontSize: 24,
              }}
            >
              Mr. John Doe
            </Text>
          </>
        </TouchableRipple>
      </Link>

      <Divider style={{ marginVertical: 16, backgroundColor: "#f0f0f0" }} />
      <DrawerItemList {...props} />
      <DrawerItem
        activeTintColor="#0A0A0A"
        inactiveTintColor="#0A0A0A"
        pressColor="transparent"
        pressOpacity={1}
        // {...props}
        icon={({ color }) => (
          <Icon source="cog-outline" size={24} color={color} />
        )}
        label={({ color }) => {
          return (
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                gap: DRAWER_ITEM_FONT_SIZE,
              }}
            >
              <Text
                style={{
                  fontFamily: "Nunito-Medium",
                  fontSize: DRAWER_ITEM_FONT_SIZE,
                }}
              >
                Settings
              </Text>
              <Icon
                source={showSettings ? "chevron-up" : "chevron-down"}
                size={24}
                color="#0A0A0A"
              />
            </View>
          );
        }}
        onPress={() => {
          setShowSettings(!showSettings);
        }}
      />
      {showSettings && (
        <>
          <DrawerItem
            label="FAQs"
            // activeBackgroundColor="#f0f0f0"
            inactiveBackgroundColor="transparent"
            activeTintColor="#0A0A0A"
            inactiveTintColor="#0A0A0A"
            labelStyle={{
              fontFamily: "Nunito-Medium",
              fontSize: DRAWER_ITEM_FONT_SIZE,
            }}
            icon={({ color }) => (
              <Icon
                source="frequently-asked-questions"
                size={24}
                color={color}
              />
            )}
            onPress={() => props.navigation.navigate("(faqs)")}
            focused={props.state.routeNames[props.state.index] === "(faqs)"}
          />
          <DrawerItem
            label="Medical References"
            // activeBackgroundColor="#f0f0f0"
            inactiveBackgroundColor="transparent"
            activeTintColor="#0A0A0A"
            inactiveTintColor="#0A0A0A"
            labelStyle={{
              fontFamily: "Nunito-Medium",
              fontSize: DRAWER_ITEM_FONT_SIZE,
            }}
            icon={({ color }) => (
              <Icon source="medical-bag" size={24} color={color} />
            )}
            onPress={() => props.navigation.navigate("(medical-references)")}
            focused={
              props.state.routeNames[props.state.index] ===
              "(medical-references)"
            }
          />
          <DrawerItem
            label="Terms and Conditions"
            // activeBackgroundColor="#f0f0f0"
            inactiveBackgroundColor="transparent"
            activeTintColor="#0A0A0A"
            inactiveTintColor="#0A0A0A"
            labelStyle={{
              fontFamily: "Nunito-Medium",
              fontSize: DRAWER_ITEM_FONT_SIZE,
            }}
            icon={({ color }) => (
              <Icon source="text-box-check-outline" size={24} color={color} />
            )}
            onPress={() => props.navigation.navigate("(terms-and-conditions)")}
            focused={
              props.state.routeNames[props.state.index] ===
              "(terms-and-conditions)"
            }
          />
          <DrawerItem
            label="About COPD Insight"
            // activeBackgroundColor="#f0f0f0"
            inactiveBackgroundColor="transparent"
            activeTintColor="#0A0A0A"
            inactiveTintColor="#0A0A0A"
            labelStyle={{
              fontFamily: "Nunito-Medium",
              fontSize: DRAWER_ITEM_FONT_SIZE,
            }}
            icon={({ color }) => (
              <Icon source="information-outline" size={24} color={color} />
            )}
            onPress={() => props.navigation.navigate("(about)")}
            focused={props.state.routeNames[props.state.index] === "(about)"}
          />
        </>
      )}
    </DrawerContentScrollView>
  );
};

export default function DrawerLayout() {
  const navigation = useNavigation();
  return (
    <>
      <StatusBar barStyle="dark-content" />
      <Drawer
        drawerContent={(props) => <CustomDrawerContent {...props} />}
        screenOptions={{
          drawerContentStyle: {
            backgroundColor: "#fff",
            width: "100%",
          },
          freezeOnBlur: true,
          drawerStyle: {
            width: "100%",
          },
          // drawerStatusBarAnimation: "fade",
          drawerActiveTintColor: "#0A0A0A",
          drawerInactiveTintColor: "#0A0A0A",
          drawerType: "slide",
          headerShadowVisible: false,
          drawerItemStyle: {
            marginVertical: 0,
            padding: 0,
          },
          drawerLabelStyle: {
            fontFamily: "Nunito-Medium",
            fontSize: DRAWER_ITEM_FONT_SIZE,
          },

          // headerLeft: () => <DrawerToggleButton />, // Default burger menu
        }}
      >
        <Drawer.Screen
          name="index"
          options={{
            header: (props) => <Header title="Dashboard" {...props} />,
            title: "Dashboard",
            drawerItemStyle: {},
            drawerIcon: ({ color }) => (
              <Icon source="account-outline" size={24} color={color} />
            ),
          }}
        />

        <Drawer.Screen
          name="(assessments)"
          options={{
            title: "My Assessments",

            headerShown: false, // Hide header here, let stack handle it
            // unmountOnBlur: true, // Not available for drawer screens

            drawerIcon: ({ color }) => (
              <Icon source="clipboard-list-outline" size={24} color={color} />
            ),
          }}
        />

        <Drawer.Screen
          name="(clinic-history)"
          options={{
            title: "My Clinic History",
            headerShown: false,
            drawerIcon: ({ color }) => (
              <Icon source="history" size={24} color={color} />
            ),
          }}
        />
        <Drawer.Screen
          name="(progress-reports)"
          options={{
            title: "My Progress Reports",
            headerShown: false,
            drawerIcon: ({ color }) => (
              <Icon source="file-document-outline" size={24} color={color} />
            ),
          }}
        />

        <Drawer.Screen
          name="(staying-healthy)"
          options={{
            title: "My Staying Healthy",
            headerShown: false,
            drawerIcon: ({ color }) => (
              <Icon source="heart-plus-outline" size={24} color={color} />
            ),
          }}
        />
        <Drawer.Screen
          name="(my-rewards)"
          options={{
            title: "My Rewards",
            headerShown: false,
            drawerIcon: ({ color }) => (
              <Icon source="gift-outline" size={24} color={color} />
            ),
          }}
        />
        <Drawer.Screen
          name="(exercise-room)"
          options={{
            title: "My Exercise Room",
            headerShown: false,
            drawerIcon: ({ color }) => (
              <Icon source="run" size={24} color={color} />
            ),
          }}
        />
        <Drawer.Screen
          name="(my-appointments)"
          options={{
            title: "My Appointments",
            headerShown: false,
            drawerIcon: ({ color }) => (
              <Icon source="calendar-clock" size={24} color={color} />
            ),
          }}
        />
        <Drawer.Screen
          name="(my-messages)"
          options={{
            title: "My Messages",
            headerShown: false,
            drawerIcon: ({ color }) => (
              <Icon source="email-outline" size={24} color={color} />
            ),
          }}
        />

        <Drawer.Screen
          name="(about)"
          options={{
            title: "About COPD Insight",
            headerShown: false,
            drawerItemStyle: {
              display: "none",
            },
            drawerIcon: ({ color }) => (
              <Icon source="information-outline" size={24} color={color} />
            ),
          }}
        />
        <Drawer.Screen
          name="(terms-and-conditions)"
          options={{
            title: "Terms and Conditions",
            headerShown: false,
            drawerItemStyle: {
              display: "none",
            },
            drawerIcon: ({ color }) => (
              <Icon source="text-box-check-outline" size={24} color={color} />
            ),
          }}
        />
        <Drawer.Screen
          name="(faqs)"
          options={{
            title: "FAQs",
            headerShown: false,
            drawerItemStyle: {
              display: "none",
            },
            drawerIcon: ({ color }) => (
              <Icon
                source="frequently-asked-questions"
                size={24}
                color={color}
              />
            ),
          }}
        />
        <Drawer.Screen
          name="(medical-references)"
          options={{
            title: "Medical References",
            drawerItemStyle: {
              display: "none",
            },
            headerShown: false,
            drawerIcon: ({ color }) => (
              <Icon source="medical-bag" size={24} color={color} />
            ),
          }}
        />
      </Drawer>
    </>
  );
}
