import { DrawerToggleButton } from "@react-navigation/drawer";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _MyMessagesLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerLeft: () => <DrawerToggleButton />,
        title: "My Messages",
        headerShadowVisible: false,
      }}
    />
  );
};

export default _MyMessagesLayout;

const styles = StyleSheet.create({});
