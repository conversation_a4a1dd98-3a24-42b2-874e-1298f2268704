import Header from "@/components/ui/Header";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _MyMessagesLayout = () => {
  return (
    <Stack
      screenOptions={{
        header: (props) => <Header title="My Messages" {...props} />,
        title: "My Messages",
        headerShadowVisible: false,
      }}
    />
  );
};

export default _MyMessagesLayout;

const styles = StyleSheet.create({});
