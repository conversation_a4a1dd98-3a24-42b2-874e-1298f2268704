import Header from "@/components/ui/Header";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _MyMessagesLayout = () => {
  return (
    <Stack
      screenOptions={{
        header: (props) => <Header title="My Messages" {...props} />,

        headerShadowVisible: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: "My Messages",
          // headerLeft: () => <DrawerToggleButton />,
        }}
      />
      <Stack.Screen
        name="compose"
        options={{
          title: "Compose",
        }}
      />
    </Stack>
  );
};

export default _MyMessagesLayout;

const styles = StyleSheet.create({});
