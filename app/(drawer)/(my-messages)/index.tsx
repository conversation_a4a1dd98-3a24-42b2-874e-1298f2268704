import <PERSON>Label from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import { Link, useNavigation } from "expo-router";
import React, { useEffect } from "react";
import {
  FlatList,
  Image,
  Pressable,
  StyleSheet,
  Text,
  View,
} from "react-native";

const MESSAGES_LIST = [
  {
    id: 1,
    user: "<PERSON>",
    title: "Request presentation",
    dataAndTime: "12-05-2023 10:30 AM",
    profilePic: require("@/assets/images/png/user.png"),
  },
  {
    id: 2,
    user: "<PERSON>",
    title: "Request presentation",
    dataAndTime: "12-05-2023 10:30 AM",
    profilePic: require("@/assets/images/png/user.png"),
  },
  {
    id: 3,
    user: "<PERSON>",
    title: "Request presentation",
    dataAndTime: "12-05-2023 10:30 AM",
    profilePic: require("@/assets/images/png/user.png"),
  },
  {
    id: 4,
    user: "<PERSON>",
    title: "Request presentation",
    dataAndTime: "12-05-2023 10:30 AM",
    profilePic: require("@/assets/images/png/user.png"),
  },
  {
    id: 5,
    user: "<PERSON>",
    title: "Request presentation",
    dataAndTime: "12-05-2023 10:30 AM",
    profilePic: require("@/assets/images/png/user.png"),
  },
  {
    id: 6,
    user: "Wade Warren",
    title: "Request presentation",
    dataAndTime: "12-05-2023 10:30 AM",
    profilePic: require("@/assets/images/png/user.png"),
  },
  {
    id: 7,
    user: "Wade Warren",
    title: "Request presentation",
    dataAndTime: "12-05-2023 10:30 AM",
    profilePic: require("@/assets/images/png/user.png"),
  },
];

const MyMessages = () => {
  const navigation = useNavigation();
  useEffect(() => {
    return () => {
      // navigation.setOptions({ title: "My Messages" });
    };
  }, []);
  return (
    <FlatList
      bounces={false}
      style={{ flex: 1 }}
      ListHeaderComponent={
        <ScreenLabel label="My Messages" style={{ marginBottom: 20 }} />
      }
      contentContainerStyle={{
        padding: SCREEN_PADDING * 2,
        paddingTop: 0,
        backgroundColor: "#fff",
        flexGrow: 1,
      }}
      data={MESSAGES_LIST}
      renderItem={({ item }) => (
        <Link href="/conversation" asChild>
          <Pressable
            style={{
              marginBottom: 20,
              padding: 20,
              borderRadius: 14,
              backgroundColor: "#0D66D00A",
              flexDirection: "row",
              alignItems: "center",
              gap: 8,
            }}
          >
            <View style={{ flex: 1, gap: 16, flexDirection: "row" }}>
              <View
                style={{
                  overflow: "hidden",
                  backgroundColor: COLORS.cardBg,
                  borderRadius: 25,
                  // padding: 6,
                  width: 50,
                  height: 50,
                }}
              >
                <Image
                  resizeMode="contain"
                  source={item.profilePic}
                  style={{
                    width: 50,
                    height: 50,
                  }}
                />
              </View>

              <View style={{ flex: 1, gap: 4 }}>
                <Text
                  style={{
                    color: COLORS.black,
                    fontFamily: "Nunito-Regular",
                    fontSize: 15,
                  }}
                >
                  {item.user}
                </Text>
                <Text
                  style={{
                    color: COLORS.gray,
                    fontFamily: "Nunito-Regular",
                    fontSize: 13,
                  }}
                >
                  {item.title}
                </Text>
                <Text
                  style={{
                    color: "#797b7c",
                    fontFamily: "Nunito-Regular",
                    fontSize: 13,
                    lineHeight: 18,
                  }}
                >
                  {item.dataAndTime}
                </Text>
              </View>
            </View>
          </Pressable>
        </Link>
      )}
      keyExtractor={(item) => item.id.toString()}
    />
  );
};

export default MyMessages;

const styles = StyleSheet.create({});
