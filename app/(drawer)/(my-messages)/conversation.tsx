import { COLORS, SCREEN_PADDING } from "@/constants";
import Feather from "@expo/vector-icons/Feather";
import React, { useEffect } from "react";
import {
  FlatList,
  Image,
  Keyboard,
  StyleSheet,
  Text,
  TextInput,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const MESSAGES_LIST = [
  {
    id: 1,
    message: "That sounds great! I’m in. What time works for you?",
    time: "12:00 PM",
    isSender: false,
  },
  {
    id: 2,
    message: "That sounds great! I’m in. What time works for you?",
    time: "12:00 PM",
    isSender: true,
  },
  {
    id: 3,
    message: "Hello",
    time: "12:00 PM",
    isSender: false,
  },
  {
    id: 4,
    message:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
    time: "12:00 PM",
    isSender: true,
  },
  //   {
  //     id: 5,
  //     message:
  //       "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
  //     time: "12:00 PM",
  //     isSender: false,
  //   },
  //   {
  //     id: 6,
  //     message: "Hello",
  //     time: "12:00 PM",
  //     isSender: true,
  //   },
  //   {
  //     id: 7,
  //     message:
  //       "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
  //     time: "12:00 PM",
  //     isSender: false,
  //   },
];

const Conversation = () => {
  const insets = useSafeAreaInsets();
  const [isKeyboardVisible, setKeyboardVisible] = React.useState(false);
  useEffect(() => {
    const showSubscription = Keyboard.addListener("keyboardDidShow", () => {
      console.log("keyboard did show");
      setKeyboardVisible(true);
    });
    const hideSubscription = Keyboard.addListener("keyboardDidHide", () => {
      console.log("keyboard did hide");
      setKeyboardVisible(false);
    });
    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);
  return (
    <View
      style={{
        flex: 1,
        padding: SCREEN_PADDING * 2,
        paddingTop: 0,
        backgroundColor: COLORS.white,
      }}
    >
      <FlatList
        data={MESSAGES_LIST}
        ItemSeparatorComponent={() => <View style={{ height: 16 }} />}
        contentContainerStyle={{
          paddingBottom: 16,
        }}
        renderItem={({ item }) => (
          <View
            style={{
              flexDirection: item.isSender ? "row-reverse" : "row",
              alignItems: "flex-end",
              gap: 8,
              //   marginBottom: 10,
              maxWidth: 305,
              alignSelf: item.isSender ? "flex-end" : "flex-start",
            }}
          >
            <View
              style={{
                overflow: "hidden",
                backgroundColor: COLORS.cardBg,
                borderRadius: 25,
                // padding: 6,
                width: 30,
                height: 30,
              }}
            >
              <Image
                resizeMode="contain"
                source={require("@/assets/images/png/user.png")}
                style={{
                  width: 30,
                  height: 30,
                }}
              />
            </View>
            <View
              style={{
                flex: 1,
                backgroundColor: item.isSender ? "#E3EBFB" : "#0D66D0",
                borderRadius: 18,
                borderBottomLeftRadius: item.isSender ? 18 : 0,
                borderBottomRightRadius: item.isSender ? 0 : 18,
                paddingVertical: 6,
                paddingHorizontal: 12,
                minHeight: 56,
                justifyContent: "center",
              }}
            >
              <Text
                style={{
                  color: item.isSender ? "#0D66D0" : "#fff",
                  fontFamily: "Nunito-Medium",
                  fontSize: 16,
                  letterSpacing: -0.41,
                  lineHeight: 22,
                }}
              >
                {item.message}
              </Text>
            </View>
          </View>
        )}
        keyExtractor={(item) => item.id.toString()}
      />
      <View
        style={{
          flexDirection: "row",
          gap: 16,
          alignItems: "center",
          height: 50,
          backgroundColor: "#0D66D00A",
          borderRadius: 25,
          //   marginTop: 16,
          paddingRight: 16,
          marginBottom: isKeyboardVisible ? insets.top * 2 : 0 /**
           ** we need to add insets.top * 2 as marginBottom to
           ** compensate for the negative keyboardVerticalOffset we have added in KeyboardAvoidingView,
           ** else the view will be cut off by the keyboard**/,
        }}
      >
        <TextInput
          placeholder="Enter Message..."
          placeholderTextColor="#70777D"
          style={{
            flex: 1,
            borderColor: "#C7C4C4",
            paddingVertical: 8,
            // paddingHorizontal: 16,
            paddingLeft: 16,
            color: COLORS.black,
            fontFamily: "Nunito-Medium",
            fontSize: 16,
          }}
        />
        <Feather name="send" size={24} color="#0D66D0" />
      </View>
    </View>
  );
};

export default Conversation;

const styles = StyleSheet.create({});
