import DropDown from "@/components/ui/DropDown";
import { COLORS, SCREEN_PADDING, SCREEN_WIDTH } from "@/constants";
import { MESSAGE_TITLES } from "@/data";
import React from "react";
import { StyleSheet, Text, TextInput } from "react-native";
import { ScrollView } from "react-native-gesture-handler";

const Compose = () => {
  const [openDropdown, setOpenDropdown] = React.useState<any>(null);
  const [selectedId, setSelectedId] = React.useState<number | string | null>(
    null
  );

  return (
    <ScrollView
      style={{
        flex: 1,
        padding: SCREEN_PADDING * 2,
        backgroundColor: COLORS.white,
      }}
    >
      <Text
        style={{
          color: COLORS.black,
          fontFamily: "Nunito-SemiBold",
          fontSize: 18,
          marginBottom: 8,
        }}
      >
        Message Title
      </Text>
      <DropDown
        onOptionSelected={(selectedId) => {
          setSelectedId(selectedId);
          setOpenDropdown(null);
        }}
        isOpen={openDropdown === "message"}
        selectedId={selectedId}
        onOpen={setOpenDropdown}
        id="message"
        options={MESSAGE_TITLES}
      />
      <Text
        style={{
          color: COLORS.black,
          fontFamily: "Nunito-SemiBold",
          fontSize: 18,
          marginBottom: 8,
          marginTop: 16,
        }}
      >
        Compose
      </Text>
      <TextInput
        placeholder="Message..."
        placeholderTextColor={COLORS.black}
        style={{
          width: SCREEN_WIDTH - 2 * (SCREEN_PADDING * 2),
          height: SCREEN_WIDTH * 0.8,
          borderWidth: 1,
          borderColor: "#C7C4C4",
          paddingVertical: 8,
          color: COLORS.black,
          fontFamily: "Nunito-Medium",
          fontSize: 16,
          borderRadius: 14,
          backgroundColor: COLORS.cardBg,
          textAlignVertical: "top",
        }}
      />
    </ScrollView>
  );
};

export default Compose;

const styles = StyleSheet.create({});
