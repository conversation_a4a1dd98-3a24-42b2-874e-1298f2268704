import { SCREEN_PADDING, SCREEN_WIDTH } from "@/constants";
import { dashboardItems } from "@/data";
import { router } from "expo-router";
import React from "react";
import { ScrollView, StyleSheet, View } from "react-native";
import { Icon, Text, TouchableRipple } from "react-native-paper";
const DASHBOARD_ITEM_MARGIN = 14;
const DASHBOARD_ITEM_WIDTH =
  (SCREEN_WIDTH - DASHBOARD_ITEM_MARGIN - SCREEN_PADDING * 4) / 2;

const Dashboard = () => {
  return (
    <View
      style={{
        flex: 1,
      }}
    >
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          padding: SCREEN_PADDING * 2,
          backgroundColor: "red",
          width: "100%",
        }}
      >
        {/* <View style={{ flex: 1 }}> */}
        <Text
          variant="headlineMedium"
          style={{
            marginBottom: 8,
            fontWeight: "bold",
            color: "#222",
          }}
        >
          How are you feeling today?
        </Text>
        <Text variant="bodyLarge" style={{ color: "#797b7c" }}>
          Thursday, May 30
        </Text>
        <View
          style={{
            flex: 1,
            // flexDirection: "row",
            flexWrap: "wrap",
            marginTop: 12,
            // justifyContent: "center",
            // backgroundColor: "red",
          }}
        >
          {dashboardItems.map((item, index) => {
            const isEven = (index + 1) % 2 === 0;
            console.log(index, isEven);

            return (
              <TouchableRipple
                key={index}
                rippleColor={"rgba(0, 0, 0, .32)"}
                onPress={() => router.push(item.linkTo as any)}
                style={{
                  width: DASHBOARD_ITEM_WIDTH,
                  height: DASHBOARD_ITEM_WIDTH * 0.8,
                  // marginLeft: isEven ? 0 : DASHBOARD_ITEM_MARGIN,
                  marginRight: isEven ? 0 : DASHBOARD_ITEM_MARGIN,
                  marginTop: DASHBOARD_ITEM_MARGIN,
                  backgroundColor: `${item.color}${item.alpha}`,
                  borderRadius: 8,
                  justifyContent: "center",
                  alignItems: "center",
                  borderWidth: 1,
                  borderColor: item.color,
                }}
              >
                <>
                  <Icon source={item.icon} size={48} color={item.color} />
                  <Text
                    variant="bodyLarge"
                    style={{
                      color: item.color,
                      textAlign: "center",
                      marginTop: 8,
                    }}
                  >
                    {item.label}
                  </Text>
                </>
              </TouchableRipple>
            );
          })}
        </View>
        {/* </View> */}
      </ScrollView>
    </View>
  );
};

export default Dashboard;

const styles = StyleSheet.create({});
