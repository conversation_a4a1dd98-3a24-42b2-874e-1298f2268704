import { SCREEN_PADDING, SCREEN_WIDTH } from "@/constants";
import { dashboardItems } from "@/data";
import { router } from "expo-router";
import React from "react";
import { ScrollView, StyleSheet, View } from "react-native";
import { Button, Icon, TouchableRipple } from "react-native-paper";
const DASHBOARD_ITEM_MARGIN = 8;
const DASHBOARD_ITEM_WIDTH =
  (SCREEN_WIDTH - DASHBOARD_ITEM_MARGIN * 4 - SCREEN_PADDING * 2) / 2;

const Dashboard = () => {
  return (
    <ScrollView
      contentContainerStyle={{
        flexGrow: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          flexDirection: "row",
          flexWrap: "wrap",
          padding: SCREEN_PADDING,
        }}
      >
        {dashboardItems.map((item, index) => (
          <TouchableRipple
            key={index}
            rippleColor={"rgba(0, 0, 0, .32)"}
            onPress={() => router.push(item.linkTo as any)}
            style={{
              width: DASHBOARD_ITEM_WIDTH,
              height: DASHBOARD_ITEM_WIDTH * 0.75,
              margin: DASHBOARD_ITEM_MARGIN,
              backgroundColor: item.color,
              borderRadius: 8,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <>
              <Icon source={item.icon} size={48} color="#fff" />
              <Button labelStyle={{ color: "#fff" }}>{item.label}</Button>
            </>
          </TouchableRipple>
        ))}
      </View>
    </ScrollView>
  );
};

export default Dashboard;

const styles = StyleSheet.create({});
