// app/(drawer)/(home)/_layout.tsx
import Head<PERSON> from "@/components/ui/Header";
import { Stack } from "expo-router";

export default function HomeLayout() {
  return (
    <Stack
      screenOptions={{
        header: (props) => <Header title="Dashboard" {...props} />,
        // Burger menu on main screen
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: "Dashboard",
          // headerLeft: () => <DrawerToggleButton />, // Burger menu on main screen
          headerShadowVisible: false,
          // header: (props) => <Header {...props} />,
        }}
      />
    </Stack>
  );
}
