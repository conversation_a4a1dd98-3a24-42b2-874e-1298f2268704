import { SCREEN_PADDING, SHADOW } from "@/constants";
import { useClinicHistoryStore } from "@/store/clinichistory.store";
import { ClinicHistoryItem } from "@/types";
import { useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import { StyleSheet, Text, View } from "react-native";

const ClinicHistoryDetails = () => {
  const [clinicHistoryItem, setClinicHistoryItem] = useState<
    ClinicHistoryItem | undefined
  >(undefined);
  const { id } = useLocalSearchParams();
  const { getClinicHistoryById } = useClinicHistoryStore();

  useEffect(() => {
    const clinicHistory = getClinicHistoryById(Number(id));
    setClinicHistoryItem(clinicHistory);
  }, [id]);

  return (
    <View style={{ flex: 1, padding: SCREEN_PADDING * 2 }}>
      <View
        style={{
          flexDirection: "row",
          gap: 10,
          alignItems: "center",
          boxShadow: SHADOW,
          borderRadius: 8,
          padding: 16,
          backgroundColor: "#fff",
          marginBottom: 10,
        }}
      >
        <Text>Performed Date</Text>
        <Text>{clinicHistoryItem?.CreatedDate.split("T")[0]}</Text>
      </View>
      <View
        style={{
          flexDirection: "row",
          gap: 10,
          alignItems: "center",
          boxShadow: SHADOW,
          borderRadius: 8,
          padding: 16,
          backgroundColor: "#fff",
          marginBottom: 10,
        }}
      >
        <Text>Performed by</Text>
        <Text>{clinicHistoryItem?.CreatedByName}</Text>
      </View>
    </View>
  );
};

export default ClinicHistoryDetails;

const styles = StyleSheet.create({});
