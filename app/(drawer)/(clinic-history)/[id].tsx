import LabelUnderline from "@/components/ui/LabelUnderline";
import Screen<PERSON>abel from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import { useClinicHistoryStore } from "@/store/clinichistory.store";
import { ClinicHistoryItem } from "@/types";
import { useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";

const ClinicHistoryItemDetail = ({
  item,
}: {
  item: { label: string; value: string };
}) => {
  return (
    <View style={{ flexDirection: "row", marginBottom: 6 }}>
      <Text
        style={{
          width: "50%",
          fontFamily: "Nunito-Regular",
          fontSize: 18,
          color: COLORS.gray,
        }}
      >
        {item.label}
      </Text>
      <Text
        style={{
          width: "50%",
          textAlign: "right",
          fontFamily: "Nunito-Regular",
          fontSize: 18,
          color: COLORS.black,
        }}
      >
        {item.value}
      </Text>
    </View>
  );
};

const ClinicHistoryDetails = () => {
  const [clinicHistoryItem, setClinicHistoryItem] = useState<
    ClinicHistoryItem | undefined
  >(undefined);
  const { id } = useLocalSearchParams();
  const { getClinicHistoryById } = useClinicHistoryStore();

  useEffect(() => {
    const clinicHistory = getClinicHistoryById(Number(id));
    setClinicHistoryItem(clinicHistory);
  }, [id]);

  return (
    <View
      style={{
        flex: 1,
        padding: SCREEN_PADDING * 2,
        paddingTop: 0,
        backgroundColor: "#fff",
      }}
    >
      <ScrollView>
        <ScreenLabel
          label="Patient Clinic History "
          style={{ marginBottom: 20 }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Age",
            value: clinicHistoryItem?.Age?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Gender",
            value: clinicHistoryItem?.Gender?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Height (cm)",
            value: clinicHistoryItem?.Height?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Weight (kg)",
            value: clinicHistoryItem?.Weight?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "BMI",
            value: clinicHistoryItem?.BMI?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "COPD History (Year of Diagnosis)",
            value: clinicHistoryItem?.CopdHistory?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Smoking Habit",
            value: clinicHistoryItem?.SmokingHabit?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Pack Years",
            value: clinicHistoryItem?.PackYears?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Shortness Of Breath",
            value: clinicHistoryItem?.ShortofBreath?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "MRC Dyspnoea Scale",
            value: clinicHistoryItem?.MRCScale?.toString() || "N/A",
          }}
        />
        <LabelUnderline label="Symptoms" />
        <ClinicHistoryItemDetail
          item={{
            label: "Cough",
            value: clinicHistoryItem?.cough?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Fatigue",
            value: clinicHistoryItem?.fatigue || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Chest Paint",
            value: clinicHistoryItem?.chestpain || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Fever",
            value: clinicHistoryItem?.fever || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Joint Pain",
            value: clinicHistoryItem?.jointpain?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Anxiety",
            value: clinicHistoryItem?.anxiety?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Depression",
            value: clinicHistoryItem?.Depression?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Smell/Taste",
            value: clinicHistoryItem?.smell?.toString() || "N/A",
          }}
        />
        <LabelUnderline label="Other Symptoms" />
        <LabelUnderline label="Other Known Medical Conditions" />
        <LabelUnderline label="Possible Respiratory Medications" />
        <LabelUnderline label="Medication Type3" />
        <LabelUnderline label="Vikas Med" />
        <LabelUnderline label="Oxygen" />
        <ClinicHistoryItemDetail
          item={{
            label: "Ambulatory Flow Rate ",
            value:
              clinicHistoryItem?.OxyAmbulatoryFlowRate?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "LTOT Flow Rate",
            value: clinicHistoryItem?.OxyLTOTFlowRate?.toString() || "N/A",
          }}
        />
        <ClinicHistoryItemDetail
          item={{
            label: "Short Burst",
            value:
              clinicHistoryItem?.OxyShortBurstFlowRate?.toString() || "N/A",
          }}
        />
        <LabelUnderline label="Clinical Observation Notes" />
      </ScrollView>
    </View>
  );
};

export default ClinicHistoryDetails;

const styles = StyleSheet.create({});
