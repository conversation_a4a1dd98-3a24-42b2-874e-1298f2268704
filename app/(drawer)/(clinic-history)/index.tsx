import { api } from "@/network/api";
import { CLINIC_HISTORY_LIST } from "@/network/apiConstants";
import { useLoaderStore } from "@/store/loader.store";
import React, { useEffect } from "react";
import { FlatList, StyleSheet, Text, View } from "react-native";

import { SCREEN_PADDING, SHADOW } from "@/constants";
import { useClinicHistoryStore } from "@/store/clinichistory.store";
import type { ClinicHistoryItem } from "@/types";
import { useRouter } from "expo-router";
import { TouchableRipple } from "react-native-paper";
const ClinicHistoryListItem = ({ item }: { item: ClinicHistoryItem }) => {
  const router = useRouter();
  return (
    <TouchableRipple
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        boxShadow: SHADOW,
        borderRadius: 8,
        padding: 16,
        backgroundColor: "#fff",
        marginBottom: 10,
      }}
      onPress={() => router.push(`/${item.Id}`)}
    >
      <>
        <View>
          <Text>Performed Date</Text>
          <Text>{item.CreatedDate.split("T")[0]}</Text>
        </View>
        <View>
          <Text>Performed by</Text>
          <Text>{item.CreatedByName}</Text>
        </View>
      </>
    </TouchableRipple>
  );
};

const MyClinicHistory = () => {
  const { clinicHistory, setClinicHistory } = useClinicHistoryStore();
  const setIsLoaderVisible = useLoaderStore(
    (state) => state.setIsLoaderVisible
  );

  useEffect(() => {
    const controller = new AbortController();
    const fetchClinicHistory = async () => {
      setIsLoaderVisible(true);
      try {
        const response = await api.get(CLINIC_HISTORY_LIST, {
          signal: controller.signal,
        });
        setClinicHistory(response.data.Data);
      } catch (error) {
      } finally {
        setIsLoaderVisible(false);
      }
    };
    fetchClinicHistory();
    return () => {
      controller.abort();
    };
  }, []);

  return (
    <View style={{ flex: 1, padding: SCREEN_PADDING * 2 }}>
      <FlatList
        data={clinicHistory}
        showsVerticalScrollIndicator={false}
        renderItem={({ item }) => <ClinicHistoryListItem item={item} />}
        keyExtractor={(item: ClinicHistoryItem) => item.Id.toString()}
      />
    </View>
  );
};

export default MyClinicHistory;

const styles = StyleSheet.create({});
