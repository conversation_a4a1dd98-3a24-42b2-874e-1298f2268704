import { api } from "@/network/api";
import { CLINIC_HISTORY_LIST } from "@/network/apiConstants";
import { useLoaderStore } from "@/store/loader.store";
import React, { useEffect } from "react";
import { FlatList, Pressable, StyleSheet, Text, View } from "react-native";

import ScreenLabel from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import { useClinicHistoryStore } from "@/store/clinichistory.store";
import type { ClinicHistoryItem } from "@/types";
import { getDateString } from "@/util";
import { Link } from "expo-router";
import { Icon } from "react-native-paper";
const ClinicHistoryListItem = ({ item }: { item: ClinicHistoryItem }) => {
  return (
    <Link href={`/${item.Id}`} asChild>
      <Pressable onPress={() => {}}>
        <View
          style={{
            flexDirection: "row",
            gap: 20,
            alignItems: "center",
            borderRadius: 14,
            justifyContent: "center",
            marginBottom: 20,
          }}
        >
          <View>
            <Text
              style={{
                color: COLORS.gray,
                fontFamily: "Nunito-Medium",
                fontSize: 14,
              }}
            >
              {getDateString(item.CreatedDate, true)}
            </Text>
          </View>
          <View
            style={{
              gap: 4,
              backgroundColor: COLORS.cardBg,
              paddingHorizontal: 14,
              paddingVertical: 18,
              borderRadius: 14,
              flex: 1,
              flexDirection: "row",
            }}
          >
            <Text
              numberOfLines={1}
              style={{
                color: COLORS.gray,
                fontFamily: "Nunito-SemiBold",
                fontSize: 16,
                flex: 1,
              }}
            >
              Performed by:{" "}
              <Text style={{ color: COLORS.black }}>{item.CreatedByName}</Text>
            </Text>

            <Icon source="arrow-right" size={24} color="#0D66D0" />
          </View>
        </View>
      </Pressable>
    </Link>
  );
};

const MyClinicHistory = () => {
  const { clinicHistory, setClinicHistory } = useClinicHistoryStore();
  const setIsLoaderVisible = useLoaderStore(
    (state) => state.setIsLoaderVisible
  );

  useEffect(() => {
    const controller = new AbortController();
    const fetchClinicHistory = async () => {
      setIsLoaderVisible(true);
      try {
        const response = await api.get(CLINIC_HISTORY_LIST, {
          signal: controller.signal,
        });
        setClinicHistory(response.data.Data);
      } catch (error) {
      } finally {
        setIsLoaderVisible(false);
      }
    };
    fetchClinicHistory();
    return () => {
      controller.abort();
    };
  }, []);

  return (
    <View
      style={{
        flex: 1,
        padding: SCREEN_PADDING * 2,
        paddingTop: 0,
        backgroundColor: "#fff",
      }}
    >
      <ScreenLabel label="My Clinic History" style={{ marginBottom: 20 }} />
      <FlatList
        data={clinicHistory}
        showsVerticalScrollIndicator={false}
        renderItem={({ item }) => <ClinicHistoryListItem item={item} />}
        keyExtractor={(item: ClinicHistoryItem) => item.Id.toString()}
      />
    </View>
  );
};

export default MyClinicHistory;

const styles = StyleSheet.create({});
