// app/(drawer)/(profile)/_layout.tsx
import Header from "@/components/ui/Header";
import { Stack } from "expo-router";

export default function ProfileLayout() {
  return (
    <Stack
      screenOptions={{
        header: (props) => <Header title="My Clinic History" {...props} />,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: "My Clinic History",
          // header: (props) => <Header title="My Clinic History" />,
          headerShadowVisible: false,
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: "Clinic History Details",
          // header: (props) => (
          //   <Header title="Clinic History Details" showBackButton={true} />
          // ),
        }}
      />
    </Stack>
  );
}
