// app/(drawer)/(profile)/_layout.tsx
import { DrawerToggleButton } from "@react-navigation/drawer";
import { Stack } from "expo-router";

export default function ProfileLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: "My Clinic History",
          headerLeft: () => <DrawerToggleButton />,
          headerShadowVisible: false,
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: "Clinic History Details",
        }}
      />
    </Stack>
  );
}
