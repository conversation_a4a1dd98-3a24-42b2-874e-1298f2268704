import Header from "@/components/ui/Header";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _TermsAndConditionsLayout = () => {
  return (
    <Stack
      screenOptions={{
        header: (props) => <Header title="Terms and Conditions" {...props} />,
        title: "Terms and Conditions",
        headerShadowVisible: false,
      }}
    />
  );
};

export default _TermsAndConditionsLayout;

const styles = StyleSheet.create({});
