import { DrawerToggleButton } from "@react-navigation/drawer";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _TermsAndConditionsLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerLeft: () => <DrawerToggleButton />,
        title: "Terms and Conditions",
        headerShadowVisible: false,
      }}
    />
  );
};

export default _TermsAndConditionsLayout;

const styles = StyleSheet.create({});
