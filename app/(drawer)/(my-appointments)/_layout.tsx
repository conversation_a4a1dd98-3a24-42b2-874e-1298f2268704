import { DrawerToggleButton } from "@react-navigation/drawer";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _MyAppointmentsLayout = () => {
  return (
    <Stack
      screenOptions={{
        title: "My Appointments",
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: "My Appointments",
          headerLeft: () => <DrawerToggleButton />,
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: "Appointment Details",
        }}
      />
    </Stack>
  );
};

export default _MyAppointmentsLayout;

const styles = StyleSheet.create({});
