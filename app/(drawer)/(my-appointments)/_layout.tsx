import { DrawerToggleButton } from "@react-navigation/drawer";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _MyAppointmentsLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerLeft: () => <DrawerToggleButton />,
        title: "My Appointments",
        headerShadowVisible: false,
      }}
    />
  );
};

export default _MyAppointmentsLayout;

const styles = StyleSheet.create({});
