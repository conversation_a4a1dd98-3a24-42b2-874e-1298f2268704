import Header from "@/components/ui/Header";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _MyAppointmentsLayout = () => {
  return (
    <Stack
      screenOptions={{
        title: "My Appointments",
        headerShadowVisible: false,
        header: (props) => <Header title="My Appointments" {...props} />,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: "My Appointments",
          // headerLeft: () => <DrawerToggleButton />,
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: "Appointment Details",
        }}
      />
      <Stack.Screen
        name="new"
        options={{
          title: "New Appointment",
        }}
      />
    </Stack>
  );
};

export default _MyAppointmentsLayout;

const styles = StyleSheet.create({});
