import DateTimePicker from "@/components/ui/DateTimePicker";
import DropDown from "@/components/ui/DropDown";
import ScreenLabel from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import { MESSAGE_TITLES } from "@/data";
import React from "react";
import { StyleSheet, Text, View } from "react-native";

const NewAppointment = () => {
  const [openDropdown, setOpenDropdown] = React.useState<any>(null);
  const [selectedId, setSelectedId] = React.useState<number | string | null>(
    null
  );
  return (
    <View
      style={{
        flex: 1,
        padding: SCREEN_PADDING * 2,
        backgroundColor: "#fff",
        paddingTop: 0,
      }}
    >
      <ScreenLabel label="New Appointment" style={{ marginBottom: 20 }} />
      <Text
        style={{
          color: COLORS.black,
          fontFamily: "Nunito-SemiBold",
          fontSize: 18,
          marginBottom: 8,
        }}
      >
        Reason For Appointment
      </Text>
      <DropDown
        onOptionSelected={(selectedId) => {
          setSelectedId(selectedId);
          setOpenDropdown(null);
        }}
        isOpen={openDropdown === "message"}
        selectedId={selectedId}
        onOpen={setOpenDropdown}
        id="message"
        options={MESSAGE_TITLES}
      />
      <View style={{ height: 20 }} />
      <View style={{ backgroundColor: COLORS.blue, borderRadius: 14 }}>
        <DateTimePicker onDateTimeChange={(date) => console.log(date)} />
      </View>
    </View>
  );
};

export default NewAppointment;

const styles = StyleSheet.create({});
