import DateTimePicker from "@/components/ui/DateTimePicker";
import DropDown from "@/components/ui/DropDown";
import ScreenLabel from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import { MESSAGE_TITLES } from "@/data";
import React from "react";
import { Pressable, StyleSheet, Text, View } from "react-native";

const NewAppointment = () => {
  const [openDropdown, setOpenDropdown] = React.useState<any>(null);
  const [selectedId, setSelectedId] = React.useState<number | string | null>(
    null
  );
  return (
    <View
      style={{
        flex: 1,
        padding: SCREEN_PADDING * 2,
        backgroundColor: "#fff",
        paddingTop: 0,
      }}
    >
      <ScreenLabel label="New Appointment" style={{ marginBottom: 20 }} />
      <Text
        style={{
          color: COLORS.black,
          fontFamily: "Nunito-SemiBold",
          fontSize: 18,
          marginBottom: 8,
          lineHeight: 20,
        }}
      >
        Reason For Appointment
      </Text>
      <DropDown
        onOptionSelected={(selectedId) => {
          setSelectedId(selectedId);
          setOpenDropdown(null);
        }}
        isOpen={openDropdown === "message"}
        selectedId={selectedId}
        onOpen={setOpenDropdown}
        id="message"
        options={MESSAGE_TITLES}
      />
      <View style={{ height: 24 }} />
      <Text
        style={{
          color: COLORS.black,
          fontFamily: "Nunito-SemiBold",
          fontSize: 18,
          marginBottom: 8,
          lineHeight: 20,
        }}
      >
        Select Date & Time
      </Text>
      <DateTimePicker onDateTimeChange={(date) => console.log(date)} />
      <Pressable
        onPress={() => console.log("Cancel Appointment")}
        style={{
          position: "absolute",
          bottom: 20,
          width: "100%",
          alignSelf: "center",
        }}
      >
        <View
          style={{
            backgroundColor: COLORS.blue,
            padding: 14,
            borderRadius: 55,
            alignItems: "center",
          }}
        >
          <Text
            style={{
              fontFamily: "Nunito-Medium",
              color: COLORS.white,
              fontSize: 16,
            }}
          >
            Confirm Appointment
          </Text>
        </View>
      </Pressable>
    </View>
  );
};

export default NewAppointment;

const styles = StyleSheet.create({});
