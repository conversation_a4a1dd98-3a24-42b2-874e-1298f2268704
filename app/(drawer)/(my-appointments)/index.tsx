import { SCREEN_PADDING } from "@/constants";
import { api } from "@/network/api";
import { APPOINTMENTS_LIST } from "@/network/apiConstants";
import { useAppointmentStore } from "@/store/appointments.store";
import { useLoaderStore } from "@/store/loader.store";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import { FlatList, StyleSheet, View } from "react-native";
import { SegmentedButtons, Text, TouchableRipple } from "react-native-paper";

type AppointmentItem = {
  Id: number;
  AppointmentDate: string;
  AppointmentTime: string;
  CheckUpType: string;
  ClinicianName: string;
};

const ListItem = ({ item }: { item: AppointmentItem }) => {
  const router = useRouter();
  return (
    <TouchableRipple
      onPress={() => router.push(`/${item.Id}`)}
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
        borderRadius: 8,
        padding: 16,
        backgroundColor: "#fff",
        marginBottom: 10,
      }}
    >
      <>
        <View>
          <Text>Appointment Date</Text>
          <Text>
            {item.AppointmentDate.split("T")[0].split("-").reverse().join("-")}
          </Text>
          <Text>Appointment Type</Text>
          <Text>{item.CheckUpType}</Text>
        </View>
        <View>
          <Text>Appointment Time</Text>
          <Text>{item.AppointmentTime}</Text>
          <Text>Clinician</Text>
          <Text>{item.ClinicianName}</Text>
        </View>
      </>
    </TouchableRipple>
  );
};

const MyAppointments = () => {
  const { appointments, setAppointments } = useAppointmentStore();
  console.log(appointments, "appointments");
  const [selectedStatus, setSelectedStatus] = useState("Pending");

  const setIsLoaderVisible = useLoaderStore(
    (state) => state.setIsLoaderVisible
  );

  useEffect(() => {
    const fetchAppointments = async () => {
      if (appointments.length > 0) setAppointments([]);
      setIsLoaderVisible(true);
      try {
        const response = await api.get(
          `${APPOINTMENTS_LIST}?appointmentStatus=${selectedStatus}`
        );
        setAppointments(response.data.Data);
      } catch (error) {
      } finally {
        setIsLoaderVisible(false);
      }
    };
    fetchAppointments();
  }, [selectedStatus]);

  return (
    <View
      style={{
        flex: 1,
        gap: 20,
        padding: SCREEN_PADDING * 2,
      }}
    >
      <SegmentedButtons
        value={selectedStatus}
        onValueChange={setSelectedStatus}
        buttons={[
          {
            value: "Pending",
            label: "Unconfirmed",
          },

          {
            value: "Scheduled",
            label: "Confirmed",
          },
          {
            value: "Cancelled",
            label: "Cancelled",
          },
        ]}
      />
      <FlatList
        data={appointments}
        showsVerticalScrollIndicator={false}
        renderItem={({ item }) => <ListItem item={item} />}
        keyExtractor={(item) => item.Id.toString()}
      />
    </View>
  );
};

export default MyAppointments;

const styles = StyleSheet.create({});
