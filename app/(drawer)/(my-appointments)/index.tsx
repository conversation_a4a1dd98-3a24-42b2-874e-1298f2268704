import { COLORS } from "@/constants";
import { api } from "@/network/api";
import { APPOINTMENTS_LIST } from "@/network/apiConstants";
import { useLoaderStore } from "@/store/loader.store";
import { getDateString } from "@/util";
import { Link, useFocusEffect, useRouter } from "expo-router";
import React, { memo, useCallback, useState } from "react";
import {
  Dimensions,
  FlatList,
  Pressable,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { TabView } from "react-native-tab-view";

const SEGMENTS = ["Confirmed", "Unconfirmed", "Cancelled"];
const SCREEN_WIDTH = Dimensions.get("window").width;
type AppointmentItem = {
  Id: number;
  AppointmentDate: string;
  AppointmentTime: string;
  CheckUpType: string;
  ClinicianName: string;
};
type Appointment = {
  id: string;
  name: string;
  time: string;
  status: "confirmed" | "unconfirmed" | "cancelled";
};
const ListItem = ({ item }: { item: AppointmentItem }) => {
  const router = useRouter();
  return (
    <View style={{ flexDirection: "row" }}>
      <View
        style={{
          width: "20%",
          justifyContent: "center",
          alignItems: "flex-start",
        }}
      >
        <Text
          style={{
            textAlign: "center",
            color: COLORS.gray,
            fontFamily: "Nunito-Medium",
            fontSize: 16,
          }}
        >
          {/* {item.CreatedDate.split("T")[0]} */}
          {/* 07 May{"\n"}2023 */}
          {getDateString(item.AppointmentDate, true)}
        </Text>
      </View>
      <Link href={`/${item.Id}`} asChild>
        <Pressable
          style={{
            flex: 1,
            backgroundColor: COLORS.cardBg,
            padding: 14,
            borderRadius: 16,
            gap: 12,
          }}
        >
          <>
            <Text
              style={{
                fontFamily: "Nunito-SemiBold",
                color: COLORS.gray,
                fontSize: 16,
                lineHeight: 18,
              }}
            >
              Appointment Type:{" "}
              <Text style={{ color: COLORS.black }}>{item.CheckUpType}</Text>
            </Text>
            <Text
              style={{
                color: COLORS.gray,
                fontFamily: "Nunito-SemiBold",
                fontSize: 16,
                lineHeight: 16,
              }}
            >
              Clinician:{" "}
              <Text style={{ color: COLORS.black }}>
                Dr. {item.ClinicianName}
              </Text>
            </Text>
            <Text
              style={{
                color: COLORS.gray,
                fontFamily: "Nunito-SemiBold",
                fontSize: 16,
                lineHeight: 16,
              }}
            >
              Appointment Time:{" "}
              <Text
                style={{
                  color: COLORS.black,
                  fontFamily: "Nunito-Medium",
                  fontSize: 16,
                  lineHeight: 16,
                }}
              >
                {item.AppointmentTime}
              </Text>
            </Text>
          </>
        </Pressable>
      </Link>
    </View>
  );
};
const DUMMY_APPOINTMENTS: Appointment[] = [
  ...Array.from({ length: 20 }, (_, i) => ({
    id: `c${i + 1}`,
    name: `Confirmed User ${i + 1}`,
    time: `${9 + (i % 10)}:00 AM`,
    status: "confirmed" as const,
  })),
  ...Array.from({ length: 20 }, (_, i) => ({
    id: `u${i + 1}`,
    name: `Unconfirmed User ${i + 1}`,
    time: `${9 + (i % 10)}:30 AM`,
    status: "unconfirmed" as const,
  })),
  ...Array.from({ length: 20 }, (_, i) => ({
    id: `x${i + 1}`,
    name: `Cancelled User ${i + 1}`,
    time: `${9 + (i % 10)}:45 AM`,
    status: "cancelled" as const,
  })),
];

const AppointmentList = memo(
  ({ status }: { status: Appointment["status"] }) => {
    console.log(status, "status");

    const [appointments, setAppointments] = useState([]);
    const setIsLoaderVisible = useLoaderStore(
      (state) => state.setIsLoaderVisible
    );
    // useEffect(() => {
    //   const fetchAppointments = async () => {
    //     if (appointments.length > 0) setAppointments([]);
    //     setIsLoaderVisible(true);
    //     try {
    //       const response = await api.get(
    //         `${APPOINTMENTS_LIST}?appointmentStatus=${status}`
    //       );
    //       setAppointments(response.data.Data);
    //     } catch (error) {
    //     } finally {
    //       setIsLoaderVisible(false);
    //     }
    //   };
    //   fetchAppointments();
    // }, [status]);

    useFocusEffect(
      useCallback(() => {
        const fetchAppointments = async () => {
          if (appointments.length > 0) setAppointments([]);
          setIsLoaderVisible(true);
          try {
            const response = await api.get(
              `${APPOINTMENTS_LIST}?appointmentStatus=${status}`
            );
            setAppointments(response.data.Data);
          } catch (error) {
          } finally {
            setIsLoaderVisible(false);
          }
        };
        fetchAppointments();
      }, [status, setIsLoaderVisible])
    );

    return (
      <FlatList
        data={appointments}
        keyExtractor={(item: AppointmentItem) => item.Id.toString()}
        contentContainerStyle={{ flexGrow: 1, justifyContent: "center" }}
        ListEmptyComponent={
          <Text style={styles.screenText}>No appointments</Text>
        }
        renderItem={({ item }) => <ListItem item={item} />}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        bounces={false}
        alwaysBounceVertical={false}
        directionalLockEnabled={true}
        ItemSeparatorComponent={() => <View style={{ height: 12 }} />}
      />
    );
  }
);

function CustomTabBar(props: any) {
  const BUTTON_HEIGHT = 40;
  return (
    <View
      style={{
        backgroundColor: "#e3f0ff",
        height: BUTTON_HEIGHT,
        borderRadius: 32,
        // margin: 12,
        alignSelf: "center",
        // justifyContent: "center",
        // paddingHorizontal: 8,
      }}
    >
      <View
        style={{
          width: "100%",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          height: BUTTON_HEIGHT,
        }}
      >
        {props.navigationState.routes.map((route: any, i: number) => {
          const focused = props.navigationState.index === i;
          return (
            <Text
              key={route.key}
              onPress={() => props.jumpTo(route.key)}
              style={{
                flex: 1,
                backgroundColor: focused ? "#4287f5" : "transparent",
                color: focused ? "#fff" : "#222",
                borderRadius: 24,
                height: BUTTON_HEIGHT,
                lineHeight: BUTTON_HEIGHT,
                fontWeight: "200",
                fontSize: 14,
                overflow: "hidden",
                // minWidth: 80,
                textAlign: "center",
              }}
            >
              {route.title}
            </Text>
          );
        })}
      </View>
    </View>
  );
}

export default function Index() {
  const [index, setIndex] = useState(0);
  const [routes] = useState([
    { key: "confirmed", title: "Confirmed" },
    { key: "unconfirmed", title: "Unconfirmed" },
    { key: "cancelled", title: "Cancelled" },
  ]);

  const renderScene = ({ route }: { route: { key: string } }) => {
    switch (route.key) {
      case "confirmed":
        return <AppointmentList status="confirmed" />;
      case "unconfirmed":
        return <AppointmentList status="unconfirmed" />;
      case "cancelled":
        return <AppointmentList status="cancelled" />;
      default:
        return null;
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: "#fff", padding: 20 }}>
      <TabView
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={setIndex}
        initialLayout={{ width: SCREEN_WIDTH }}
        renderTabBar={CustomTabBar}
        lazy={true}
        // lazyPreloadDistance={0}
        swipeEnabled={true}
        animationEnabled={true}
        pagerStyle={{ flex: 1 }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  screenText: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    marginTop: 32,
  },
  appointmentItemFull: {
    padding: 16,
    marginVertical: 8,
    width: "100%",
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    elevation: 2,
    alignSelf: "stretch",
  },
  appointmentName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#222",
  },
  appointmentTime: {
    fontSize: 16,
    color: "#666",
    marginTop: 4,
  },
});
