import ScreenLabel from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import { api } from "@/network/api";
import { APPOINTMENTS_LIST } from "@/network/apiConstants";
import { useAppointmentStore } from "@/store/appointments.store";
import { useLoaderStore } from "@/store/loader.store";
import { getDateString } from "@/util";
import { Link, useFocusEffect, useRouter } from "expo-router";
import React, { memo, useCallback, useState } from "react";
import {
  Dimensions,
  FlatList,
  Pressable,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { TabView } from "react-native-tab-view";

const SEGMENTS = ["Confirmed", "Unconfirmed", "Cancelled"];
const SCREEN_WIDTH = Dimensions.get("window").width;
type AppointmentItem = {
  Id: number;
  AppointmentDate: string;
  AppointmentTime: string;
  CheckUpType: string;
  ClinicianName: string;
  AppointmentStatus: string;
};
type Appointment = {
  id: string;
  name: string;
  time: string;
  status: "Scheduled" | "Pending" | "Cancelled";
};
const ListItem = ({ item }: { item: AppointmentItem }) => {
  const router = useRouter();
  return (
    <View style={{ flexDirection: "row" }}>
      <View
        style={{
          width: "20%",
          justifyContent: "center",
          alignItems: "flex-start",
        }}
      >
        <Text
          style={{
            textAlign: "center",
            color: COLORS.gray,
            fontFamily: "Nunito-Medium",
            fontSize: 16,
          }}
        >
          {/* {item.CreatedDate.split("T")[0]} */}
          {/* 07 May{"\n"}2023 */}
          {getDateString(item.AppointmentDate, true)}
        </Text>
      </View>
      <Link href={`/${item.Id}?status=${item.AppointmentStatus}`} asChild>
        <Pressable
          style={{
            flex: 1,
            backgroundColor: COLORS.cardBg,
            padding: 14,
            borderRadius: 16,
            gap: 12,
          }}
        >
          <>
            <Text
              style={{
                fontFamily: "Nunito-SemiBold",
                color: COLORS.gray,
                fontSize: 16,
                lineHeight: 18,
              }}
            >
              Appointment Type:{" "}
              <Text style={{ color: COLORS.black }}>{item.CheckUpType}</Text>
            </Text>
            <Text
              style={{
                color: COLORS.gray,
                fontFamily: "Nunito-SemiBold",
                fontSize: 16,
                lineHeight: 16,
              }}
            >
              Clinician:{" "}
              <Text style={{ color: COLORS.black }}>
                Dr. {item.ClinicianName}
              </Text>
            </Text>
            <Text
              style={{
                color: COLORS.gray,
                fontFamily: "Nunito-SemiBold",
                fontSize: 16,
                lineHeight: 16,
              }}
            >
              Appointment Time:{" "}
              <Text
                style={{
                  color: COLORS.black,
                  fontFamily: "Nunito-Medium",
                  fontSize: 16,
                  lineHeight: 16,
                }}
              >
                {item.AppointmentTime}
              </Text>
            </Text>
          </>
        </Pressable>
      </Link>
    </View>
  );
};

const AppointmentList = memo(
  ({
    status,
    isActive,
    
  }: {
    status: Appointment["status"];
    isActive: boolean;
  }) => {

    console.log(status, "status", "isActive:", isActive);
    const { getAppointmentsByStatus, setAppointments } = useAppointmentStore();
    const [refreshing, setRefreshing] = useState(false);
    const setIsLoaderVisible = useLoaderStore(
      (state) => state.setIsLoaderVisible
    );
    // useEffect(() => {
    //   const fetchAppointments = async () => {
    //     if (appointments.length > 0) setAppointments([]);
    //     setIsLoaderVisible(true);
    //     try {
    //       const response = await api.get(
    //         `${APPOINTMENTS_LIST}?appointmentStatus=${status}`
    //       );
    //       setAppointments(response.data.Data);
    //     } catch (error) {
    //     } finally {
    //       setIsLoaderVisible(false);
    //     }
    //   };
    //   fetchAppointments();
    // }, [status]);

    const fetchAppointments = useCallback(
      async (showLoader = true) => {
        if (showLoader) {
          setIsLoaderVisible(true);
        } else {
          setRefreshing(true);
        }

        try {
          const response = await api.get(
            `${APPOINTMENTS_LIST}?appointmentStatus=${status}`
          );
          setAppointments(response.data.Data, status);
        } catch (error) {
          console.error("Error fetching appointments:", error);
        } finally {
          if (showLoader) {
            setIsLoaderVisible(false);
          } else {
            setRefreshing(false);
          }
        }
      },
      [status, setIsLoaderVisible]
    );

    useFocusEffect(
      useCallback(() => {
        // Only fetch data if this tab is active
        if (!isActive) {
          return;
        }

        fetchAppointments(true);
      }, [isActive, fetchAppointments])
    );

    const onRefresh = useCallback(() => {
      if (isActive) {
        fetchAppointments(false);
      }
    }, [isActive, fetchAppointments]);


    return (
      <FlatList
        style={{ flex: 1 }}
        data={getAppointmentsByStatus(status)}
        keyExtractor={(item: AppointmentItem) => item.Id.toString()}
        contentContainerStyle={{
          paddingBottom: 20,
        }}
        ItemSeparatorComponent={() => <View style={{ height: 20 }} />}
        ListEmptyComponent={
          <Text style={styles.screenText}>No appointments</Text>
        }
        renderItem={({ item }) => <ListItem item={item} />}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        bounces={false}
        alwaysBounceVertical={false}
        directionalLockEnabled={true}
        removeClippedSubviews={true}
        maxToRenderPerBatch={6}
        windowSize={6}
        refreshing={refreshing}
        onRefresh={onRefresh}
      />
    );
  }
);

function CustomTabBar(props: any) {
  const BUTTON_HEIGHT = 40;
  return (
    <View
      style={{
        backgroundColor: "#e3f0ff",
        height: BUTTON_HEIGHT,
        borderRadius: 32,
        // margin: 12,
        alignSelf: "center",
        // justifyContent: "center",
        // paddingHorizontal: 8
        marginBottom: 20,
      }}
    >
      <View
        style={{
          width: "100%",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          height: BUTTON_HEIGHT,
        }}
      >
        {props.navigationState.routes.map((route: any, i: number) => {
          const focused = props.navigationState.index === i;
          return (
            <Text
              key={route.key}
              onPress={() => props.jumpTo(route.key)}
              style={{
                flex: 1,
                backgroundColor: focused ? "#4287f5" : "transparent",
                color: focused ? "#fff" : "#222",
                borderRadius: 24,
                height: BUTTON_HEIGHT,
                lineHeight: BUTTON_HEIGHT,
                fontWeight: "200",
                fontSize: 14,
                overflow: "hidden",
                // minWidth: 80,
                textAlign: "center",
              }}
            >
              {route.title}
            </Text>
          );
        })}
      </View>
    </View>
  );
}

export default function Index() {
  const [isSwipeEnabled, setIsSwipeEnabled] = useState(true);
  const [index, setIndex] = useState(0);
  const [routes] = useState([
    { key: "confirmed", title: "Confirmed" },
    { key: "unconfirmed", title: "Unconfirmed" },
    { key: "cancelled", title: "Cancelled" },
  ]);

  const renderScene = ({ route }: { route: { key: string } }) => {
    // Check if this is the currently active tab
    const currentRoute = routes[index];
    const isActive = route.key === currentRoute.key;

    switch (route.key) {
      case "confirmed":
        return <AppointmentList status="Scheduled" isActive={isActive} />;
      case "unconfirmed":
        return <AppointmentList status="Pending" isActive={isActive} />;
      case "cancelled":
        return <AppointmentList status="Cancelled" isActive={isActive} />;
      default:
        return null;
    }
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: "#fff",
        padding: SCREEN_PADDING * 2,
        paddingTop: 0,
      }}
    >
      <ScreenLabel label="My Appointments" style={{ marginBottom: 20 }} />
      <TabView
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={setIndex}
        initialLayout={{ width: SCREEN_WIDTH }}
        renderTabBar={CustomTabBar}
        lazy={true}
        lazyPreloadDistance={0}
        swipeEnabled={true}
        animationEnabled={true}
        pagerStyle={{ flex: 1 }}
        style={{ flex: 1 }}
       
      />
    </View>
  );
}

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  screenText: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    marginTop: 32,
  },
  appointmentItemFull: {
    padding: 16,
    marginVertical: 8,
    width: "100%",
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    elevation: 2,
    alignSelf: "stretch",
  },
  appointmentName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#222",
  },
  appointmentTime: {
    fontSize: 16,
    color: "#666",
    marginTop: 4,
  },
});
