import ScreenLabel from "@/components/ui/ScreenLabel";
import SegmentedButtons from "@/components/ui/SegmentedButtons";
import { COLORS, SCREEN_PADDING } from "@/constants";
import { api } from "@/network/api";
import { APPOINTMENTS_LIST } from "@/network/apiConstants";
import { useAppointmentStore } from "@/store/appointments.store";
import { useLoaderStore } from "@/store/loader.store";
import { getDateString } from "@/util";
import { Link, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import { FlatList, Pressable, StyleSheet, Text, View } from "react-native";

type AppointmentItem = {
  Id: number;
  AppointmentDate: string;
  AppointmentTime: string;
  CheckUpType: string;
  ClinicianName: string;
};

const ListItem = ({ item }: { item: AppointmentItem }) => {
  const router = useRouter();
  return (
    <View style={{ flexDirection: "row" }}>
      <View
        style={{
          width: "20%",
          justifyContent: "center",
          alignItems: "flex-start",
        }}
      >
        <Text
          style={{
            textAlign: "center",
            color: COLORS.gray,
            fontFamily: "Nunito-Medium",
            fontSize: 16,
          }}
        >
          {/* {item.CreatedDate.split("T")[0]} */}
          {/* 07 May{"\n"}2023 */}
          {getDateString(item.AppointmentDate, true)}
        </Text>
      </View>
      <Link href={`/${item.Id}`} asChild>
        <Pressable
          style={{
            flex: 1,
            backgroundColor: COLORS.cardBg,
            padding: 14,
            borderRadius: 16,
            gap: 12,
          }}
        >
          <>
            <Text
              style={{
                fontFamily: "Nunito-SemiBold",
                color: COLORS.gray,
                fontSize: 16,
                lineHeight: 18,
              }}
            >
              Appointment Type:{" "}
              <Text style={{ color: COLORS.black }}>{item.CheckUpType}</Text>
            </Text>
            <Text
              style={{
                color: COLORS.gray,
                fontFamily: "Nunito-SemiBold",
                fontSize: 16,
                lineHeight: 16,
              }}
            >
              Clinician:{" "}
              <Text style={{ color: COLORS.black }}>
                Dr. {item.ClinicianName}
              </Text>
            </Text>
            <Text
              style={{
                color: COLORS.gray,
                fontFamily: "Nunito-SemiBold",
                fontSize: 16,
                lineHeight: 16,
              }}
            >
              Appointment Time:{" "}
              <Text
                style={{
                  color: COLORS.black,
                  fontFamily: "Nunito-Medium",
                  fontSize: 16,
                  lineHeight: 16,
                }}
              >
                {item.AppointmentTime}
              </Text>
            </Text>
          </>
        </Pressable>
      </Link>
    </View>
  );
};

const MyAppointments = () => {
  const { appointments, setAppointments } = useAppointmentStore();
  const [selectedStatus, setSelectedStatus] = useState("Pending");

  const setIsLoaderVisible = useLoaderStore(
    (state) => state.setIsLoaderVisible
  );

  useEffect(() => {
    const fetchAppointments = async () => {
      if (appointments.length > 0) setAppointments([]);
      setIsLoaderVisible(true);
      try {
        const response = await api.get(
          `${APPOINTMENTS_LIST}?appointmentStatus=${selectedStatus}`
        );
        setAppointments(response.data.Data);
      } catch (error) {
      } finally {
        setIsLoaderVisible(false);
      }
    };
    fetchAppointments();
  }, [selectedStatus]);

  return (
    <View
      style={{
        flex: 1,
        padding: SCREEN_PADDING * 2,
        paddingTop: 0,
        backgroundColor: COLORS.white,
      }}
    >
      <ScreenLabel label="My Appointments" style={{ marginBottom: 20 }} />
      <SegmentedButtons
        value={selectedStatus}
        onValueChange={setSelectedStatus}
        buttons={[
          {
            value: "Pending",
            label: "Unconfirmed",
          },

          {
            value: "Scheduled",
            label: "Confirmed",
          },
          {
            value: "Cancelled",
            label: "Cancelled",
          },
        ]}
      />

      <View style={{ height: 20 }} />
      <FlatList
        data={appointments}
        showsVerticalScrollIndicator={false}
        renderItem={({ item }) => <ListItem item={item} />}
        keyExtractor={(item) => item.Id.toString()}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
};

export default MyAppointments;

const styles = StyleSheet.create({
  separator: {
    height: 20,
  },
});
