import { useAppointmentStore } from "@/store/appointments.store";
import { useLocalSearchParams } from "expo-router";
import React, { useEffect } from "react";
import { StyleSheet, View } from "react-native";

import { SCREEN_PADDING } from "@/constants";
import type { Appointment } from "@/store/appointments.store";
import { Icon, Text } from "react-native-paper";

const AppointmentDetails = () => {
  const [appointment, setAppointment] = React.useState<Appointment | undefined>(
    undefined
  );
  console.log(appointment, "appointment");

  const getAppointmentById = useAppointmentStore(
    (state) => state.getAppointmentById
  );
  const { id } = useLocalSearchParams();
  useEffect(() => {
    const appointment = getAppointmentById(Number(id));
    setAppointment(appointment);
  }, [id, getAppointmentById]);

  return (
    <View style={{ flex: 1, padding: SCREEN_PADDING * 2 }}>
      <View
        style={{
          flexDirection: "row",
          gap: 10,
          alignItems: "center",
          boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
          borderRadius: 8,
          padding: 16,
          backgroundColor: "#fff",
          marginBottom: 10,
        }}
      >
        <Icon source="account-outline" size={24} color="#000" />
        <View>
          <Text>Appointment With</Text>
          <Text variant="titleMedium">Dr. {appointment?.ClinicianName}</Text>
        </View>
      </View>

      <View
        style={{
          flexDirection: "row",
          gap: 10,
          alignItems: "center",
          boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
          borderRadius: 8,
          padding: 16,
          backgroundColor: "#fff",
          marginBottom: 10,
        }}
      >
        <Icon source="heart-outline" size={24} color="#000" />
        <View>
          <Text>Checkup Type</Text>
          <Text variant="titleMedium">{appointment?.CheckUpType}</Text>
        </View>
      </View>

      <View
        style={{
          flexDirection: "row",
          gap: 10,
          alignItems: "center",
          boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
          borderRadius: 8,
          padding: 16,
          backgroundColor: "#fff",
          marginBottom: 10,
        }}
      >
        <Icon source="calendar-clock" size={24} color="#000" />
        <View>
          <Text>Date Of Appointment</Text>
          <Text variant="titleMedium">{appointment?.AppointmentDate}</Text>
        </View>
      </View>

      <View
        style={{
          flexDirection: "row",
          gap: 10,
          alignItems: "center",
          boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
          borderRadius: 8,
          padding: 16,
          backgroundColor: "#fff",
          marginBottom: 10,
        }}
      >
        <Icon source="clock-outline" size={24} color="#000" />
        <View>
          <Text>Appointment Status</Text>
          <Text variant="titleMedium">{appointment?.AppointmentStatus}</Text>
        </View>
      </View>
    </View>
  );
};

export default AppointmentDetails;

const styles = StyleSheet.create({});
