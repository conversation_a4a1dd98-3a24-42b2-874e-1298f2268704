import { useAppointmentStore } from "@/store/appointments.store";
import { useLocalSearchParams } from "expo-router";
import React, { useEffect } from "react";
import { StyleSheet, View } from "react-native";

import ScreenLabel from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import type { Appointment } from "@/store/appointments.store";
import { Icon, Text, TouchableRipple } from "react-native-paper";

const AppointmentDetails = () => {
  const [appointment, setAppointment] = React.useState<Appointment | undefined>(
    undefined
  );
  console.log(appointment, "appointment");

  const getAppointmentById = useAppointmentStore(
    (state) => state.getAppointmentById
  );
  const { id } = useLocalSearchParams();
  useEffect(() => {
    const appointment = getAppointmentById(Number(id));
    setAppointment(appointment);
  }, [id, getAppointmentById]);

  return (
    <View
      style={{ flex: 1, padding: SCREEN_PADDING * 2, backgroundColor: "#fff" }}
    >
      <ScreenLabel
        label="Appointment Details"
        style={{ marginBottom: 20, marginTop: 0 }}
      />
      <View
        style={{
          flexDirection: "row",
          gap: 20,
          alignItems: "center",
          borderRadius: 14,
          padding: 16,
          backgroundColor: COLORS.cardBg,
          marginBottom: 10,
        }}
      >
        <Icon source="account" size={24} color="#0D66D0" />
        <View>
          <Text
            style={{
              fontFamily: "Nunito-SemiBold",
              color: COLORS.gray,
              fontSize: 16,
              lineHeight: 24,
            }}
          >
            Appointment With
          </Text>
          <Text
            style={{
              fontFamily: "Nunito-SemiBold",
              color: COLORS.black,
              fontSize: 16,
              lineHeight: 24,
            }}
          >
            Dr. {appointment?.ClinicianName}
          </Text>
        </View>
      </View>
      <View
        style={{
          flexDirection: "row",
          gap: 20,
          alignItems: "center",
          borderRadius: 14,
          padding: 16,
          backgroundColor: COLORS.cardBg,
          marginBottom: 10,
        }}
      >
        <Icon source="heart" size={24} color="#0D66D0" />
        <View>
          <Text
            style={{
              fontFamily: "Nunito-SemiBold",
              color: COLORS.gray,
              fontSize: 16,
              lineHeight: 24,
            }}
          >
            Check Up Type
          </Text>
          <Text
            style={{
              fontFamily: "Nunito-SemiBold",
              color: COLORS.black,
              fontSize: 16,
              lineHeight: 24,
            }}
          >
            {appointment?.CheckUpType}
          </Text>
        </View>
      </View>
      <View
        style={{
          flexDirection: "row",
          gap: 20,
          alignItems: "center",
          borderRadius: 14,
          padding: 16,
          backgroundColor: COLORS.cardBg,
          marginBottom: 10,
        }}
      >
        <Icon source="calendar-clock" size={24} color="#0D66D0" />
        <View>
          <Text
            style={{
              fontFamily: "Nunito-SemiBold",
              color: COLORS.gray,
              fontSize: 16,
              lineHeight: 24,
            }}
          >
            Appointment Date & Time
          </Text>
          <Text
            style={{
              fontFamily: "Nunito-SemiBold",
              color: COLORS.black,
              fontSize: 16,
              lineHeight: 24,
            }}
          >
            {appointment?.AppointmentDate.split("T")[0] +
              " & " +
              appointment?.AppointmentTime}
          </Text>
        </View>
      </View>

      <View
        style={{
          flexDirection: "row",
          gap: 20,
          alignItems: "center",
          borderRadius: 14,
          padding: 16,
          backgroundColor: COLORS.cardBg,
          marginBottom: 10,
        }}
      >
        <Icon source="list-status" size={24} color="#0D66D0" />
        <View
          style={{
            flex: 1,
            flexDirection: "row",
            gap: 16,
            alignItems: "center",
          }}
        >
          <Text
            style={{
              fontFamily: "Nunito-SemiBold",
              color: COLORS.gray,
              fontSize: 16,
              lineHeight: 24,
            }}
          >
            Status:
          </Text>
          <Text
            style={{
              fontFamily: "Nunito-Medium",
              color: COLORS.blue,
              fontSize: 14,
              backgroundColor: `${COLORS.blue}1A`,
              paddingVertical: 6,
              paddingHorizontal: 16,
              borderRadius: 50,
              borderWidth: 1,
              borderColor: COLORS.blue,
            }}
          >
            {appointment?.AppointmentStatus}
          </Text>
        </View>
      </View>
      <TouchableRipple
        onPress={() => console.log("Cancel Appointment")}
        style={{
          position: "absolute",
          bottom: 20,
          width: "100%",
          alignSelf: "center",
        }}
      >
        <View
          style={{
            backgroundColor: COLORS.blue,
            padding: 14,
            borderRadius: 55,
            alignItems: "center",
          }}
        >
          <Text
            style={{
              fontFamily: "Nunito-Medium",
              color: COLORS.white,
              fontSize: 16,
            }}
          >
            Reschedule
          </Text>
        </View>
      </TouchableRipple>
    </View>
  );
};

export default AppointmentDetails;

const styles = StyleSheet.create({});
