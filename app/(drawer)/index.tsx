import { SCREEN_PADDING, SCREEN_WIDTH } from "@/constants";
import { DASHBOARD_ITEMS } from "@/data";
import { useNavigation } from "@react-navigation/native";
import React from "react";
import { Text as RnText, ScrollView, StyleSheet, View } from "react-native";
import { Icon, TouchableRipple } from "react-native-paper";
const DASHBOARD_ITEM_MARGIN = 14;
const DASHBOARD_ITEM_WIDTH =
  (SCREEN_WIDTH - DASHBOARD_ITEM_MARGIN - SCREEN_PADDING * 4) / 2;

const getCurrentDateString = () => {
  const date = new Date();
  const options: Intl.DateTimeFormatOptions = {
    weekday: "long",
    month: "long",
    day: "numeric",
  };
  return date.toLocaleDateString("en-US", options);
};

const Dashboard = () => {
  const navigation = useNavigation();
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: "#fff",
      }}
    >
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          padding: SCREEN_PADDING * 2,
          flexGrow: 1,
          paddingTop: 0,
        }}
      >
        {/* <View style={{ flex: 1 }}> */}
        <RnText
          style={{
            marginBottom: 8,
            color: "#0A0A0A",
            fontFamily: "Nunito-Bold",
            fontSize: 32,
          }}
        >
          How are you feeling{"\n"}today?
        </RnText>
        <RnText
          style={{
            color: "#797b7c",
            fontSize: 20,
            fontFamily: "Nunito-Medium",
          }}
        >
          {getCurrentDateString()}
        </RnText>
        <View
          style={{
            flex: 1,
            flexDirection: "row",
            flexWrap: "wrap",
            marginTop: 12,
            // justifyContent: "center",
            // backgroundColor: "red",
          }}
        >
          {DASHBOARD_ITEMS.map((item, index) => {
            const isEven = (index + 1) % 2 === 0;

            return (
              <TouchableRipple
                key={index}
                rippleColor={"rgba(0, 0, 0, .1)"}
                onPress={() => navigation.navigate(item.linkTo as never)}
                style={{
                  width: DASHBOARD_ITEM_WIDTH,
                  height: DASHBOARD_ITEM_WIDTH * 0.8,
                  // marginLeft: isEven ? 0 : DASHBOARD_ITEM_MARGIN,
                  marginRight: isEven ? 0 : DASHBOARD_ITEM_MARGIN,
                  marginTop: DASHBOARD_ITEM_MARGIN,
                  backgroundColor: `${item.color}${item.alpha}`,
                  borderRadius: 8,
                  justifyContent: "center",
                  alignItems: "center",
                  borderWidth: 1,
                  borderColor: item.color,
                }}
              >
                <>
                  <Icon source={item.icon} size={48} color={item.color} />
                  <RnText
                    style={{
                      color: item.color,
                      textAlign: "center",
                      marginTop: 8,
                      fontFamily: "Nunito-SemiBold",
                      fontSize: 16,
                    }}
                  >
                    {item.label}
                  </RnText>
                </>
              </TouchableRipple>
            );
          })}
        </View>
        {/* </View> */}
      </ScrollView>
    </View>
  );
};

export default Dashboard;

const styles = StyleSheet.create({});
