import { SCREEN_PADDING } from "@/constants";
import { MEDICAL_REFERENCES } from "@/data";
import { useLoaderStore } from "@/store/loader.store";
import { useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import { StyleSheet, View } from "react-native";
import WebView from "react-native-webview";

const MedicalReferencesDetails = () => {
  const setIsLoaderVisible = useLoaderStore(
    (state) => state.setIsLoaderVisible
  );
  const [medicalReference, setMedicalReference] = useState<
    (typeof MEDICAL_REFERENCES)[0] | undefined
  >(undefined);
  const { id } = useLocalSearchParams();
  useEffect(() => {
    const medicalReference = MEDICAL_REFERENCES.find(
      (item) => item.id === Number(id)
    );
    setMedicalReference(medicalReference);
  }, [id]);

  return (
    <View style={{ flex: 1, padding: SCREEN_PADDING, backgroundColor: "#fff" }}>
      <WebView
        source={{ uri: medicalReference?.webLink as string }}
        onLoadStart={() => setIsLoaderVisible(true)}
        onLoad={() => setIsLoaderVisible(false)}
        onError={() => setIsLoaderVisible(false)}
      />
    </View>
  );
};

export default MedicalReferencesDetails;

const styles = StyleSheet.create({});
