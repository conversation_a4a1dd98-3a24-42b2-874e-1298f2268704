import ScreenLabel from "@/components/ui/ScreenLabel";
import { SCREEN_PADDING } from "@/constants";
import { MEDICAL_REFERENCES } from "@/data";
import { Link } from "expo-router";
import React from "react";
import { StyleSheet, View } from "react-native";
import { Icon, Text, TouchableRipple } from "react-native-paper";

const MedicalReferences = () => {
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: "#fff",
        padding: SCREEN_PADDING * 2,
        paddingTop: 0,
      }}
    >
      <ScreenLabel label="Medical References" style={{ marginBottom: 20 }} />
      {MEDICAL_REFERENCES.map((item) => (
        <View
          key={item.id}
          style={{
            marginBottom: 16,
            padding: 20,
            borderRadius: 8,
            backgroundColor: "#0D66D00A",
          }}
        >
          <Text style={{ color: "#666" }} variant="titleMedium">
            {item.description}
          </Text>
          <Link href={`/${item.id}`} asChild>
            <TouchableRipple
              rippleColor={"#fff"}
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginTop: 10,
              }}
            >
              <>
                <Text>{item.webLink}</Text>
                <Icon source="arrow-right" size={24} color="#496DDB" />
              </>
            </TouchableRipple>
          </Link>
        </View>
      ))}
    </View>
  );
};

export default MedicalReferences;

const styles = StyleSheet.create({});
