import { DrawerToggleButton } from "@react-navigation/drawer";
import { Stack } from "expo-router";

const _MedicalReferencesLayout = () => {
  return (
    <Stack
      screenOptions={{
        title: "Medical References",
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: "Medical References",
          headerLeft: () => <DrawerToggleButton />,
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: "Medical Reference Details",
        }}
      />
    </Stack>
  );
};

export default _MedicalReferencesLayout;
