import { DrawerToggleButton } from "@react-navigation/drawer";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _MyRewardsLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerLeft: () => <DrawerToggleButton />,
        title: "My Rewards",
        headerShadowVisible: false,
      }}
    />
  );
};

export default _MyRewardsLayout;

const styles = StyleSheet.create({});
