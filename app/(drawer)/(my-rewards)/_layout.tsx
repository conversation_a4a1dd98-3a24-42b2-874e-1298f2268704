import Header from "@/components/ui/Header";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _MyRewardsLayout = () => {
  return (
    <Stack
      screenOptions={{
        header: (props) => <Header title="My Rewards" {...props} />,
        title: "My Rewards",
        headerShadowVisible: false,
      }}
    />
  );
};

export default _MyRewardsLayout;

const styles = StyleSheet.create({});
