import ScreenLabel from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import React from "react";
import { Image, StyleSheet, Text, View } from "react-native";

const Rewards = () => {
  return (
    <View
      style={{
        flex: 1,
        padding: SCREEN_PADDING * 2,
        backgroundColor: COLORS.white,
        paddingTop: 0,
      }}
    >
      <ScreenLabel label="My Rewards" style={{ marginBottom: 20 }} />
      <View style={{ alignItems: "center", gap: 8 }}>
        <Text
          style={{
            color: COLORS.black,
            fontFamily: "Nunito-SemiBold",
            fontSize: 16,
            textAlign: "center",
            marginTop: 16,
          }}
        >
          Congratulations!{"\n"}
          You have been awarded Top Star Trophy.
        </Text>
        <Image
          style={{ width: 276, height: 207 }}
          source={require("../../../assets/images/rewards-start.gif")}
        />
        <Text
          style={{
            color: COLORS.black,
            fontFamily: "Nunito-SemiBold",
            fontSize: 16,
          }}
        >
          Total points earned
        </Text>
        <Text
          style={{
            color: COLORS.black,
            fontFamily: "Nunito-Bold",
            fontSize: 24,
          }}
        >
          1234
        </Text>
        <Text
          style={{
            color: COLORS.black,
            fontFamily: "Nunito-Bold",
            fontSize: 18,
            textAlign: "center",
            marginTop: 24,
          }}
        >
          You are 10th out of 72 {"\n"}participants
        </Text>
        <Text
          style={{
            color: COLORS.black,
            fontFamily: "Nunito-SemiBold",
            fontSize: 16,
            marginTop: 24,
          }}
        >
          Keep up the good work.
        </Text>
      </View>
    </View>
  );
};

export default Rewards;

const styles = StyleSheet.create({});
