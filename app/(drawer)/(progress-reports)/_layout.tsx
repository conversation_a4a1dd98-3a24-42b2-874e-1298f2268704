import Header from "@/components/ui/Header";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const ProgressReportLayout = () => {
  return (
    <Stack
      screenOptions={{
        header: (props) => <Header title="My Progress Reports" {...props} />,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: "My Progress Reports",
          headerShadowVisible: false,
        }}
      />
    </Stack>
  );
};

export default ProgressReportLayout;

const styles = StyleSheet.create({});
