import { DrawerToggleButton } from "@react-navigation/drawer";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const ProgressReportLayout = () => {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: "My Progress Reports",
          headerLeft: () => <DrawerToggleButton />, // Burger menu on main screen
          headerShadowVisible: false,
        }}
      />
    </Stack>
  );
};

export default ProgressReportLayout;

const styles = StyleSheet.create({});
