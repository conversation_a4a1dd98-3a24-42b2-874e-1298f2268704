import CollapsableFAQItem from "@/components/ui/CollapsableFAQItem";
import { SCREEN_PADDING } from "@/constants";
import { api } from "@/network/api";
import { FAQS } from "@/network/apiConstants";
import { useLoaderStore } from "@/store/loader.store";
import React, { useEffect } from "react";
import { FlatList, StyleSheet, View } from "react-native";

const FAQs = () => {
  const [faqs, setFaqs] = React.useState([]);
  const setLoader = useLoaderStore((state) => state.setIsLoaderVisible);
  const [selectedItem, setSelectedItem] = React.useState<number | null>(null);

  const handleSelectedItem = (id: number) => {
    setSelectedItem(id === selectedItem ? null : id);
  };

  useEffect(() => {
    const fetchFAQs = async () => {
      setLoader(true);
      try {
        const response = await api.get(FAQS);
        setFaqs(response.data.Data);
      } catch (error) {
      } finally {
        setLoader(false);
      }
    };
    fetchFAQs();
  }, []);

  return (
    <View
      style={{
        flex: 1,
        padding: SCREEN_PADDING * 2,
        backgroundColor: "#fff",
      }}
    >
      <FlatList
        data={faqs}
        contentContainerStyle={{ gap: 16 }}
        showsVerticalScrollIndicator={false}
        renderItem={({ item }: { item: any }) => (
          <CollapsableFAQItem
            question={item.Question}
            answer={item.Answer}
            id={item.Id}
            onSelect={handleSelectedItem}
            isOpen={selectedItem === item.Id}
          />
        )}
        keyExtractor={(item) => item.Id.toString()}
      />
    </View>
  );
};

export default FAQs;

const styles = StyleSheet.create({});
