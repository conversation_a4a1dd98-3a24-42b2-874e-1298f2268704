import CollapsableFAQItem from "@/components/ui/CollapsableFAQItem";
import { SCREEN_PADDING } from "@/constants";
import { api } from "@/network/api";
import { FAQS } from "@/network/apiConstants";
import React, { useEffect } from "react";
import { FlatList, StyleSheet, View } from "react-native";

const FAQs = () => {
  const [faqs, setFaqs] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  useEffect(() => {
    const fetchFAQs = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await api.get(FAQS);
        setFaqs(response.data.Data);
      } catch (error) {
        setError(error instanceof Error ? error.message : "An error occurred");
      } finally {
        setLoading(false);
      }
    };
    fetchFAQs();
  }, []);

  return (
    <View
      style={{
        flex: 1,
        padding: SCREEN_PADDING,
      }}
    >
      <FlatList
        data={faqs}
        ItemSeparatorComponent={() => <View style={{ height: 20 }} />}
        renderItem={({ item }) => (
          <CollapsableFAQItem question={item.Question} answer={item.Answer} />
        )}
        keyExtractor={(item) => item.Id.toString()}
      />
    </View>
  );
};

export default FAQs;

const styles = StyleSheet.create({});
