import Header from "@/components/ui/Header";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _FAQsLayout = () => {
  return (
    <Stack
      screenOptions={{
        // headerLeft: () => <DrawerToggleButton />,
        header: (props) => <Header title="FAQs" {...props} />,
        title: "FAQs",
        headerShadowVisible: false,
      }}
    />
  );
};

export default _FAQsLayout;

const styles = StyleSheet.create({});
