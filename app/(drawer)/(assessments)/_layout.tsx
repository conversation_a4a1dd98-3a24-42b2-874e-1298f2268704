// app/(drawer)/(home)/_layout.tsx
import { DrawerToggleButton } from "@react-navigation/drawer";
import { Stack } from "expo-router";

export default function AssementsLayout() {
  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
        // <PERSON> menu on main screen
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: "Assessments",
          headerLeft: () => <DrawerToggleButton />,
        }}
      />
      <Stack.Screen
        name="newAssessment"
        options={{
          title: "New Assessment",
        }}
      />
      <Stack.Screen
        name="my-assessments"
        options={{
          title: "Assessment Records",
        }}
      />
      <Stack.Screen
        name="spirometry-instructions"
        options={{
          title: "My Assessment - Spirometry",
        }}
      />
    </Stack>
  );
}
