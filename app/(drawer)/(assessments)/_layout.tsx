// app/(drawer)/(home)/_layout.tsx
import Head<PERSON> from "@/components/ui/Header";
import { Stack } from "expo-router";

export default function AssementsLayout() {
  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
        // Burger menu on main screen
        header: (props) => <Header title="My Assessments" {...props} />,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          // header: (props) => <Header />,
          title: "",
          // headerLeft: (props) => <Header />,
        }}
      />
      <Stack.Screen
        name="newAssessment"
        options={{
          title: "New Assessment",
        }}
      />

      <Stack.Screen
        name="spirometry-instructions"
        options={{
          title: "My Assessment - Spirometry",
        }}
      />
      <Stack.Screen
        name="wellbeing-assessment"
        options={{
          title: "Wellbeing-Assessment",
        }}
      />
    </Stack>
  );
}
