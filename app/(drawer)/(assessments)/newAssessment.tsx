import ElevatedSurfaceCard from "@/components/ui/ElevatedSurfaceCard";
import SegmentedButtonsComponent from "@/components/ui/SegmentedButtonsComponent";
import { router } from "expo-router";
import React from "react";
import { StyleSheet, View } from "react-native";
import { Button } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const NewAssessment = () => {
  const [value, setValue] = React.useState("Wellbeing");
  const selectedOption = (val: string) => {
    setValue(val);
  };
  const insets = useSafeAreaInsets();

  const handlePress = (val: string) => {
    if (val === "Wellbeing") {
      router.push("/(drawer)/(assessments)/wellbeing-assessment");
    } else if (val === "Biomarkers") {
      console.log("Go to Biomarkers Screen");
    } else {
      console.log("No such option");
    }
  };
  const spirometryHandlePress = () =>
    router.push("/(drawer)/(assessments)/spirometry-instructions");

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <View style={styles.optionsContainer}>
        <SegmentedButtonsComponent value={value} handlePress={selectedOption} />
        {value === "Wellbeing" && (
          <>
            <ElevatedSurfaceCard
              title="Welcome to My Self-Assessment"
              description={`Here you have a set of questions and a choice of answers from which you can choose how you feel today. ${"\n"}Completing the Self-Assessment will help you monitor your wellbeing from day to day and also help you and your clinical team pick up any changes in your condition early. ${"\n"}If you have any other concerns today please write these in the text box at the end of the wellbeing diary. When you have completed your self-assessment, please press on the Submit button so that the information is sent to your clinical team.`}
            />
            <ElevatedSurfaceCard
              title="Note:"
              titleStyle={{ color: "#D2042D" }}
              description={`Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.`}
            />
          </>
        )}
        {value === "Spiro / O2" && (
          <ElevatedSurfaceCard
            title="Spirometry"
            description={`Perform your spirometry assessment${"\n"} using a smart lung monitor as${"\n"} recommended`}
            handlePress={spirometryHandlePress}
          />
        )}
        {value === "Biomarkers" && (
          <>
            <ElevatedSurfaceCard
              title="Welcome to My Biomarkers"
              description={`Biomarkers are substances that are found in the bloodstream which can increase in amount during a flare-up of COPD.${"\n"} The amount of each biomarker in blood will depend on the severity of the COPD inflammation and any infection associated with a flare-up. The levels of biomarkers may also increase because of other conditions and the clinical team will advise you on your test results.`}
            />
            <ElevatedSurfaceCard
              title="Note:"
              titleStyle={{ color: "#D2042D" }}
              description={`Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.`}
            />
          </>
        )}
      </View>
      {(value === "Wellbeing" || value === "Biomarkers") && (
        <Button mode="contained" onPress={() => handlePress(value)}>
          Start Assessment
        </Button>
      )}
    </View>
  );
};

export default NewAssessment;

const styles = StyleSheet.create({
  container: {
    // backgroundColor: "yellow",
    justifyContent: "space-between",
    flex: 1,
    padding: 20,
  },
  optionsContainer: {
    gap: 20,
  },
});
