import { api } from "@/network/api";
import { MSP_LINE } from "@/network/apiConstants";
import { useLoaderStore } from "@/store/loader.store";
import {
  getFev1Percentage,
  getFVCPercentage,
} from "@/util/calculateFEV1FVCPercentage";
import { spirometryFormValidation } from "@/validations/schema";
import { useFormik } from "formik";
import React, { useEffect, useState } from "react";
import { Alert, StyleSheet, View } from "react-native";
import { Button, Surface, Text, TextInput } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const SpirometryForm = () => {
  type userData = { Fev1: number; FVC: number; Height: number; Age: number };
  type userDetails = { Data: userData };
  const [userInfo, setUserInfo] = useState<userDetails>();
  const setLoaderVisibility = useLoaderStore(
    (state) => state.setIsLoaderVisible
  );
  const insets = useSafeAreaInsets();
  const formik = useFormik({
    initialValues: {
      Id: 0,
      PId: 0,
      PatientName: "string",
      NHSId: "string",
      CreatedByName: "string",
      ClientId: 0,
      FEV1: null,
      FEV1Percentage: "",
      FVC: null,
      FVCPercentage: "",
      CreatedBy: 0,
      UpdatedBy: 0,
      CreatedDate: "2025-07-11T04:03:26.496Z",
      UpdatedDate: "2025-07-11T04:03:26.496Z",
    },
    validationSchema: spirometryFormValidation,
    onSubmit: (values) => {},
  });

  const submitHandler = () => {
    formik.handleSubmit();
  };

  useEffect(() => {
    const fetchClientData = async () => {
      try {
        setLoaderVisibility(true);
        // const getUserDetails: any = await AsyncStorage.getItem("UserDetails"); //replace with actual patient ID
        const clientData = await api.get(`${MSP_LINE}?patientId=0`); //replace with actual patient ID
        setUserInfo(clientData.data);
        setLoaderVisibility(false);
      } catch (error) {
        console.log("Error! spirometry-form fetchClientData()", error);
        Alert.alert("Error!", "Failed to fetch Client Information");
        setLoaderVisibility(false);
      }
    };
    fetchClientData();
  }, []);

  const setFEV1Value = (val: String) => {
    formik.setFieldValue("FEV1", val);
    if (!userInfo) return;
    getFev1Percentage(userInfo).then((data) => {
      formik.setFieldValue(
        "FEV1Percentage",
        val ? String(((Number(val) / Number(data)) * 100).toFixed(2)) : ""
      );
    });
  };
  const setFVCValue = (val: String) => {
    formik.setFieldValue("FVC", val);
    if (!userInfo) return;
    getFVCPercentage(userInfo).then((data) => {
      formik.setFieldValue(
        "FVCPercentage",
        val ? String(((Number(val) / Number(data)) * 100).toFixed(2)) : ""
      );
    });
  };
  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <View style={styles.section}>
        <View style={styles.deviceBar}>
          <Text variant="titleMedium">Connected to : </Text>
          <Text variant="titleMedium" style={styles.connectedDevice}>
            No device connected
          </Text>
          <Text variant="titleMedium" style={styles.deviceSearchOption}>
            Click here to search for devices
          </Text>
        </View>
        <View style={styles.formContainer}>
          <Surface style={styles.surface}>
            <View style={styles.inputsRowContainer}>
              <TextInput
                style={{ flex: 1 }}
                label="FEV1 (L)"
                mode="outlined"
                keyboardType="decimal-pad"
                onChangeText={setFEV1Value}
              />
              <TextInput
                style={{ flex: 1 }}
                label="FEV1 (% Pred)"
                mode="outlined"
                editable={false}
                value={String(formik.values.FEV1Percentage)}
              />
            </View>
            <Text style={styles.error}>
              {formik.touched.FEV1 && formik.errors.FEV1}
            </Text>
          </Surface>
          <Surface style={styles.surface}>
            <View style={styles.inputsRowContainer}>
              <TextInput
                style={{ flex: 1 }}
                label="FVC (L)"
                mode="outlined"
                keyboardType="decimal-pad"
                onChangeText={setFVCValue}
              />
              <TextInput
                style={{ flex: 1 }}
                label="FVC (% Pred)"
                mode="outlined"
                editable={false}
                value={String(formik.values.FVCPercentage)}
              />
            </View>
            <Text style={styles.error}>
              {formik.touched.FVC && formik.errors.FVC}
            </Text>
          </Surface>
          <Surface style={styles.surface}>
            <View style={styles.inputsRowContainer}>
              <TextInput
                style={{ flex: 1 }}
                label="FEV1 TO FVC RATIO (%)"
                mode="outlined"
                editable={false}
                value={
                  (formik.values.FEV1 &&
                    formik.values.FVC &&
                    String(
                      (
                        Number(formik.values.FEV1 / formik.values.FVC) * 100
                      ).toFixed(2)
                    )) ||
                  undefined
                }
              />
            </View>
          </Surface>
        </View>
      </View>
      <Button
        style={styles.submitButton}
        mode="contained"
        onPress={submitHandler}
      >
        Submit
      </Button>
    </View>
  );
};

export default SpirometryForm;

const styles = StyleSheet.create({
  container: {
    // backgroundColor: "yellow",
    justifyContent: "space-between",
    flex: 1,
    paddingTop: 20,
  },
  section: {
    // backgroundColor: "red",
    gap: 20,
  },
  deviceBar: {
    backgroundColor: "#14a2ea11",
    padding: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  connectedDevice: {
    paddingTop: 10,
    color: "#14a2ea",
  },
  deviceSearchOption: {
    color: "#14a2ea",
  },
  formContainer: { padding: 20, gap: 20 },
  surface: { borderRadius: 8 },
  inputsRowContainer: {
    flexDirection: "row",
    // backgroundColor: "red",
    justifyContent: "space-evenly",
    gap: 10,
    padding: 10,
    paddingVertical: 20,
  },
  error: { color: "red", padding: 5 },
  submitButton: { marginHorizontal: 20 },
});
