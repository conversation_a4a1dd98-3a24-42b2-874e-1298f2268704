import { router } from "expo-router";
import React from "react";
import { StyleSheet, View } from "react-native";
import { Button, Text } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const SpirometryInstructions = () => {
  const insets = useSafeAreaInsets();
  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <View style={styles.instructionsContainer}>
        <Text variant="titleLarge">Instructions</Text>
        <View style={styles.instructionElement}>
          <View style={styles.instructionIcon}>
            <Text style={styles.instructionIconText}>1</Text>
          </View>
          <Text style={styles.instructionText}>
            Press Start Test at the bottom and Insert a disposable mouthpiece to
            lung monitor.
          </Text>
        </View>
        <View style={styles.instructionElement}>
          <View style={styles.instructionIcon}>
            <Text style={styles.instructionIconText}>2</Text>
          </View>
          <Text style={styles.instructionText}>Fully breathe in and hold.</Text>
        </View>
        <View style={styles.instructionElement}>
          <View style={styles.instructionIcon}>
            <Text style={styles.instructionIconText}>3</Text>
          </View>
          <Text style={styles.instructionText}>
            Blow out as hard as possible for 6 seconds. You may be asked ti
            breathe out up to 3 times to get a consistent reading.
          </Text>
        </View>
        <View style={styles.instructionElement}>
          <View style={styles.instructionIcon}>
            <Text style={styles.instructionIconText}>4</Text>
          </View>
          <Text style={styles.instructionText}>
            Press the enter button on the lung monitor and it will transfer all
            the results directly to your COPD Insight App.
          </Text>
        </View>
        <Text style={styles.userNotice}>
          <Text style={{ color: "red" }}>Please note : </Text> The hand-held
          lung monitor uses FEV6 as a result volume to reflect the more widely
          used FVC on bigger spirometers. This will not alter the clinical
          interpretation of your results.
        </Text>
      </View>

      <Button
        mode="contained"
        onPress={() => router.push("/(drawer)/(assessments)/spirometry-form")}
      >
        Start Assessment
      </Button>
    </View>
  );
};

export default SpirometryInstructions;

const styles = StyleSheet.create({
  container: {
    // backgroundColor: "yellow",
    justifyContent: "space-between",
    flex: 1,
    padding: 20,
  },
  instructionsContainer: {
    // backgroundColor: "red",
    gap: 20,
  },
  instructionElement: {
    // backgroundColor: "green",
    flexDirection: "row",
    alignItems: "center",
  },
  instructionIcon: {
    width: "10%",
    backgroundColor: "#14a2ea",
    justifyContent: "center",
    alignItems: "center",
    aspectRatio: 1,
    borderRadius: 999,
  },
  instructionIconText: { color: "#fff" },
  instructionText: { width: "90%", paddingLeft: 10 },
  userNotice: {
    textAlign: "center",
  },
});
