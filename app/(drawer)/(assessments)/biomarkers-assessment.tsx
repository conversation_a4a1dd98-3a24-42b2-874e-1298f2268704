import { COLORS } from "@/constants";
import { api } from "@/network/api";
import { BIOMETRICS_FORM } from "@/network/apiConstants";
import { useLoaderStore } from "@/store/loader.store";
import { BiometricsAssessment } from "@/types";
import { biomarkersFormValidation } from "@/validations/schema";
import { router } from "expo-router";
import { useFormik } from "formik";
import React from "react";
import { StyleSheet, Text, TextInput, View } from "react-native";
import { Button } from "react-native-paper";

const BiomarkersAssessment = () => {
  const setLoaderVisibility = useLoaderStore(
    (state) => state.setIsLoaderVisible
  );
  const submitForm = async (values: BiometricsAssessment) => {
    try {
      setLoaderVisibility(true);
      const result = await api.post(BIOMETRICS_FORM, values);
      console.log(result.data.Message); // Replace with a toast
      router.dismissTo("/");
    } catch (error) {
      console.log("Error! wellbeing-assessment screen : submitForm", error);
    } finally {
      setLoaderVisibility(false);
    }
  };

  const formik = useFormik({
    initialValues: {
      CRP: "",
      PCT: "",
    },
    onSubmit: submitForm,
    validationSchema: biomarkersFormValidation,
  });
  return (
    <View style={styles.container}>
      <View>
        <Text style={styles.title}>Biomarkers Level</Text>
        <TextInput
          value={formik.values.CRP}
          style={styles.userTextInput}
          placeholder="C-Reactive Protein (CRP) mg/L"
          placeholderTextColor={COLORS.black}
          onChangeText={formik.handleChange("CRP")}
          keyboardType="decimal-pad"
        />
        {formik.touched.CRP && formik.errors.CRP && (
          <Text style={styles.validationMessage}>{formik.errors.CRP}</Text>
        )}
        <TextInput
          value={formik.values.PCT}
          style={styles.userTextInput}
          placeholder="Procalcitonic (PCT) ng/mL"
          placeholderTextColor={COLORS.black}
          onChangeText={formik.handleChange("PCT")}
          keyboardType="decimal-pad"
        />
      </View>
      <Button
        mode="contained"
        labelStyle={{ fontFamily: "Nunito-SemiBold", fontSize: 16 }}
        style={{ marginVertical: 20 }}
        onPress={() => formik.handleSubmit()}
      >
        Submit
      </Button>
    </View>
  );
};

export default BiomarkersAssessment;

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    justifyContent: "space-between",
    flex: 1,
    padding: 20,
  },
  optionsContainer: {
    // backgroundColor: "olive",
    // gap: 10,
  },
  title: { fontFamily: "Nunito-SemiBold", fontSize: 16 },
  userTextInput: {
    borderWidth: 1,
    borderRadius: 14,
    borderColor: COLORS.borderGray,
    marginTop: 20,
    backgroundColor: COLORS.cardBg,
    fontFamily: "Nunito-SemiBold",
    fontSize: 16,
    color: COLORS.black,
    paddingHorizontal: 10,
  },
  validationMessage: {
    // backgroundColor: "red",
    fontFamily: "Nunito-SemiBold",
    fontSize: 12,
    color: "#D2042D",
    paddingTop: 5,
  },
});
