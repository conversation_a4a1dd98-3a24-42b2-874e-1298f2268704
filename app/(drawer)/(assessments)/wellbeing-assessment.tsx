import DropDown from "@/components/ui/DropDown";
import RadioButtonsGroup from "@/components/ui/RadioButtonsGroup";
import { COLORS } from "@/constants";
import {
  ANTIBIOTICS_OPTIONS,
  BREATHING_TODAY_OPTIONS,
  COUGH_TODAY_OPTIONS,
  DA<PERSON>Y_LIVING_OPTIONS,
  HAVE_YOU_OPTIONS,
  PHYSICAL_ACTIVITY_OPTIONS,
  SPUTUM_COLOR_OPTIONS,
  SPUTUM_OPTIONS,
  SWELLING_OPTIONS,
  TIREDNESS_OPTIONS,
} from "@/constants/strings";
import { api } from "@/network/api";
import { WELLBEING_FORM } from "@/network/apiConstants";
import { useLoaderStore } from "@/store/loader.store";
import { WellBeingAssessment } from "@/types";
import { wellbeingFormValidation } from "@/validations/schema";
import { router } from "expo-router";
import { useFormik } from "formik";
import React, { useCallback, useState } from "react";
import { StyleSheet, Text, View } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import { Button, TextInput } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const WellbeingAssessment = () => {
  const [openDropdown, setOpenDropdown] = useState<String | null>();
  const insets = useSafeAreaInsets();
  const setLoaderVisibility = useLoaderStore(
    (state) => state.setIsLoaderVisible
  );

  const submitForm = async (values: WellBeingAssessment) => {
    try {
      setLoaderVisibility(true);
      const result = await api.post(WELLBEING_FORM, values);
      console.log(result.data.Message); // Replace with a toast
      router.dismissTo("/");
    } catch (error) {
      console.log("Error! wellbeing-assessment screen : submitForm", error);
    } finally {
      setLoaderVisibility(false);
    }
  };

  const formik = useFormik({
    initialValues: {
      BreathingToday: 0,
      BreathingAffectingDailyActivities: 0,
      PhysicalActivity: 0,
      HaveCoughToday: false,
      HowIsCough: 0,
      SputumProducingToday: 0,
      ColorOfSputum: 0,
      FeelTiredToday: false,
      IsYourTiredness: 0,
      AnkleSwelling: false,
      IsAnkleSwelling: 0,
      ChestPainTightness: false,
      Heartburn: false,
      HeartburnWokenAtNight: false,
      FlareUpToday: false,
      HaveYou: 0,
      TreatmentAntibiotics: 0,
      HospitalisedForExacerbation: false,
      RecievingTreatmentFlareUp: false,
      AnyOtherAntibiotics: "",
      TreatmentOralSteroid: "",
      TreatmentOther: "",
      OtherConcern: "",
    },
    onSubmit: submitForm,
    validationSchema: wellbeingFormValidation,
  });

  const resetAntibioticsSection = () => {
    formik.setFieldValue("TreatmentAntibiotics", 0);
    formik.setFieldValue("AnyOtherAntibiotics", "");
    formik.setFieldValue("TreatmentOralSteroid", "");
    formik.setFieldValue("TreatmentOther", "");
  };

  // Radio button selections
  const haveCoughTodaySelection = useCallback((selection: boolean) => {
    formik.setFieldValue("HowIsCough", 0);
    formik.setFieldValue("HaveCoughToday", selection);
  }, []);
  const feelTiredTodaySelection = useCallback((selection: boolean) => {
    formik.setFieldValue("IsYourTiredness", 0);
    formik.setFieldValue("FeelTiredToday", selection);
  }, []);
  const ankleSwellingSelection = useCallback((selection: boolean) => {
    formik.setFieldValue("IsAnkleSwelling", 0);
    formik.setFieldValue("AnkleSwelling", selection);
  }, []);
  const chestPainTightnessSelection = useCallback(
    (selection: boolean) =>
      formik.setFieldValue("ChestPainTightness", selection),
    []
  );
  const heartburnSelection = useCallback((selection: boolean) => {
    formik.setFieldValue("HeartburnWokenAtNight", false);
    formik.setFieldValue("Heartburn", selection);
  }, []);
  const heartburnWokenAtNightSelection = useCallback(
    (selection: boolean) =>
      formik.setFieldValue("HeartburnWokenAtNight", selection),
    []
  );
  const flareUpTodaySelection = useCallback((selection: boolean) => {
    formik.setFieldValue("HaveYou", 0);
    formik.setFieldValue("HospitalisedForExacerbation", false);
    formik.setFieldValue("RecievingTreatmentFlareUp", false);
    resetAntibioticsSection();
    formik.setFieldValue("FlareUpToday", selection);
  }, []);
  const hospitalisedForExacerbationSelection = useCallback(
    (selection: boolean) => {
      formik.setFieldValue("RecievingTreatmentFlareUp", false);
      resetAntibioticsSection();
      formik.setFieldValue("HospitalisedForExacerbation", selection);
    },
    []
  );
  const recievingTreatmentFlareUpSelection = useCallback(
    (selection: boolean) => {
      resetAntibioticsSection();
      formik.setFieldValue("RecievingTreatmentFlareUp", selection);
    },
    []
  );

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 300 }} // Remove later
      >
        <View style={styles.optionsContainer}>
          <Text style={styles.optionLabel}>How is your breathing today?</Text>
          <View style={styles.optionDropdownContainer}>
            <DropDown
              id="BreathingToday"
              placeholder="Choose Option"
              isOpen={openDropdown === "BreathingToday"}
              onOpen={(id) => setOpenDropdown(id)}
              onOptionSelected={(id) => {
                formik.setFieldValue("BreathingToday", id);
                setOpenDropdown(null);
              }}
              options={BREATHING_TODAY_OPTIONS}
              zIndex={100}
              selectedId={formik.values.BreathingToday}
            />
          </View>
          {formik.touched.BreathingToday && formik.errors.BreathingToday && (
            <Text style={styles.validationMessage}>
              {formik.errors.BreathingToday}
            </Text>
          )}
          <Text style={[styles.optionLabel]}>
            How is your breathing affecting your ability to perform?
          </Text>
          <View style={styles.divider} />
          <View style={styles.subOptionContainer}>
            <Text style={styles.subOptionLabel}>
              Activities of daily living e.g. self-washing/dress, cooking,
              housework.
            </Text>
          </View>
          <View style={styles.optionDropdownContainer}>
            <DropDown
              id="BreathingAffectingDailyActivities"
              placeholder="Choose Option"
              isOpen={openDropdown === "BreathingAffectingDailyActivities"}
              onOpen={(id) => setOpenDropdown(id)}
              onOptionSelected={(id) => {
                formik.setFieldValue("BreathingAffectingDailyActivities", id);
                setOpenDropdown(null);
              }}
              options={DAILY_LIVING_OPTIONS}
              zIndex={99}
              selectedId={formik.values.BreathingAffectingDailyActivities}
            />
          </View>
          {formik.touched.BreathingToday &&
            formik.errors.BreathingAffectingDailyActivities && (
              <Text style={styles.validationMessage}>
                {formik.errors.BreathingAffectingDailyActivities}
              </Text>
            )}
          <View style={styles.subOptionContainer}>
            <Text style={styles.subOptionLabel}>
              Physical activities e.g. walking, shopping, gardening.
            </Text>
          </View>
          <View style={styles.optionDropdownContainer}>
            <DropDown
              id="PhysicalActivity"
              placeholder="Choose Option"
              isOpen={openDropdown === "PhysicalActivity"}
              onOpen={(id) => setOpenDropdown(id)}
              onOptionSelected={(id) => {
                formik.setFieldValue("PhysicalActivity", id);
                setOpenDropdown(null);
              }}
              options={PHYSICAL_ACTIVITY_OPTIONS}
              zIndex={98}
              selectedId={formik.values.PhysicalActivity}
            />
          </View>
          {formik.touched.PhysicalActivity &&
            formik.errors.PhysicalActivity && (
              <Text style={styles.validationMessage}>
                {formik.errors.PhysicalActivity}
              </Text>
            )}

          <Text style={styles.optionLabel}>Do you have cough today?</Text>
          <RadioButtonsGroup
            value={formik.values["HaveCoughToday"]}
            setValue={haveCoughTodaySelection}
            style={styles.radiobuttonOption}
          />

          {formik.values.HaveCoughToday && (
            <>
              <Text style={styles.optionLabel}>How is your cough?</Text>
              <View style={styles.optionDropdownContainer}>
                <DropDown
                  id="HowIsCough"
                  placeholder="Choose Option"
                  isOpen={openDropdown === "HowIsCough"}
                  onOpen={(id) => setOpenDropdown(id)}
                  onOptionSelected={(id) => {
                    formik.setFieldValue("HowIsCough", id);
                    setOpenDropdown(null);
                  }}
                  options={COUGH_TODAY_OPTIONS}
                  zIndex={97}
                  selectedId={formik.values.HowIsCough}
                />
              </View>
              {formik.touched.HowIsCough && formik.errors.HowIsCough && (
                <Text style={styles.validationMessage}>
                  {formik.errors.HowIsCough}
                </Text>
              )}
            </>
          )}

          <Text style={styles.optionLabel}>
            How much sputum are your producing today?
          </Text>
          <View style={styles.optionDropdownContainer}>
            <DropDown
              id="SputumProducingToday"
              placeholder="Choose Option"
              isOpen={openDropdown === "SputumProducingToday"}
              onOpen={(id) => setOpenDropdown(id)}
              onOptionSelected={(id) => {
                formik.setFieldValue("ColorOfSputum", 0);
                formik.setFieldValue("SputumProducingToday", id);
                setOpenDropdown(null);
              }}
              options={SPUTUM_OPTIONS}
              zIndex={96}
              selectedId={formik.values.SputumProducingToday}
            />
          </View>
          {formik.touched.SputumProducingToday &&
            formik.errors.SputumProducingToday && (
              <Text style={styles.validationMessage}>
                {formik.errors.SputumProducingToday}
              </Text>
            )}

          {[2, 3, 4, 5].includes(formik.values.SputumProducingToday) && (
            <>
              <Text style={styles.optionLabel}>
                What is the color of your sputum today?
              </Text>
              <View style={styles.optionDropdownContainer}>
                <DropDown
                  id="ColorOfSputum"
                  placeholder="Choose Option"
                  isOpen={openDropdown === "ColorOfSputum"}
                  onOpen={(id) => setOpenDropdown(id)}
                  onOptionSelected={(id) => {
                    formik.setFieldValue("ColorOfSputum", id);
                    setOpenDropdown(null);
                  }}
                  options={SPUTUM_COLOR_OPTIONS}
                  zIndex={95}
                  selectedId={formik.values.ColorOfSputum}
                />
              </View>
              {formik.touched.ColorOfSputum && formik.errors.ColorOfSputum && (
                <Text style={styles.validationMessage}>
                  {formik.errors.ColorOfSputum}
                </Text>
              )}
            </>
          )}

          <Text style={styles.optionLabel}>Do you feel tired?</Text>
          <RadioButtonsGroup
            value={formik.values["FeelTiredToday"]}
            setValue={feelTiredTodaySelection}
            style={styles.radiobuttonOption}
          />

          {formik.values.FeelTiredToday && (
            <>
              <Text style={styles.optionLabel}>Is your tiredness?</Text>
              <View style={styles.optionDropdownContainer}>
                <DropDown
                  id="IsYourTiredness"
                  placeholder="Choose Option"
                  isOpen={openDropdown === "IsYourTiredness"}
                  onOpen={(id) => setOpenDropdown(id)}
                  onOptionSelected={(id) => {
                    formik.setFieldValue("IsYourTiredness", id);
                    setOpenDropdown(null);
                  }}
                  options={TIREDNESS_OPTIONS}
                  zIndex={94}
                  selectedId={formik.values.IsYourTiredness}
                />
              </View>
              {formik.touched.IsYourTiredness &&
                formik.errors.IsYourTiredness && (
                  <Text style={styles.validationMessage}>
                    {formik.errors.IsYourTiredness}
                  </Text>
                )}
            </>
          )}

          <Text style={styles.optionLabel}>
            Do you have ankle swelling today?
          </Text>
          <RadioButtonsGroup
            value={formik.values["AnkleSwelling"]}
            setValue={ankleSwellingSelection}
            style={styles.radiobuttonOption}
          />

          {formik.values.AnkleSwelling && (
            <>
              <Text style={styles.optionLabel}>Is your swelling?</Text>
              <View style={styles.optionDropdownContainer}>
                <DropDown
                  id="IsAnkleSwelling"
                  placeholder="Choose Option"
                  isOpen={openDropdown === "IsAnkleSwelling"}
                  onOpen={(id) => setOpenDropdown(id)}
                  onOptionSelected={(id) => {
                    formik.setFieldValue("IsAnkleSwelling", id);
                    setOpenDropdown(null);
                  }}
                  options={SWELLING_OPTIONS}
                  zIndex={93}
                  selectedId={formik.values.IsAnkleSwelling}
                />
              </View>
              {formik.touched.IsAnkleSwelling &&
                formik.errors.IsAnkleSwelling && (
                  <Text style={styles.validationMessage}>
                    {formik.errors.IsAnkleSwelling}
                  </Text>
                )}
            </>
          )}

          <Text style={styles.optionLabel}>
            Do you have chest pain/tightness today?
          </Text>
          <RadioButtonsGroup
            value={formik.values["ChestPainTightness"]}
            setValue={chestPainTightnessSelection}
            style={styles.radiobuttonOption}
          />

          <Text style={styles.optionLabel}>
            Have you had any heartburn in the last 24 hours?
          </Text>
          <RadioButtonsGroup
            value={formik.values["Heartburn"]}
            setValue={heartburnSelection}
            style={styles.radiobuttonOption}
          />

          {formik.values.Heartburn && (
            <>
              <Text style={styles.optionLabel}>
                Has the heartburn woken you up at night?
              </Text>
              <RadioButtonsGroup
                value={formik.values["HeartburnWokenAtNight"]}
                setValue={heartburnWokenAtNightSelection}
                style={styles.radiobuttonOption}
              />
            </>
          )}

          <Text style={styles.optionLabel}>
            Do you feel you have a flare-up of your COPD today?
          </Text>
          <RadioButtonsGroup
            value={formik.values["FlareUpToday"]}
            setValue={flareUpTodaySelection}
            style={styles.radiobuttonOption}
          />

          {formik.values.FlareUpToday && (
            <>
              <Text style={styles.optionLabel}>Have you?</Text>
              <View style={styles.optionDropdownContainer}>
                <DropDown
                  id="HaveYou"
                  placeholder="Choose Option"
                  isOpen={openDropdown === "HaveYou"}
                  onOpen={(id) => setOpenDropdown(id)}
                  onOptionSelected={(id) => {
                    formik.setFieldValue("HospitalisedForExacerbation", false);
                    formik.setFieldValue("RecievingTreatmentFlareUp", false);
                    resetAntibioticsSection();
                    formik.setFieldValue("HaveYou", id);
                    setOpenDropdown(null);
                  }}
                  options={HAVE_YOU_OPTIONS}
                  zIndex={92}
                  selectedId={formik.values.HaveYou}
                />
              </View>
              {formik.touched.HaveYou && formik.errors.HaveYou && (
                <Text style={styles.validationMessage}>
                  {formik.errors.HaveYou}
                </Text>
              )}
            </>
          )}
          {formik.values.HaveYou === 4 && (
            <>
              <Text style={styles.optionLabel}>
                Have you had to be hospitalised for your exacerbation?
              </Text>
              <RadioButtonsGroup
                value={formik.values["HospitalisedForExacerbation"]}
                setValue={hospitalisedForExacerbationSelection}
                style={styles.radiobuttonOption}
              />
            </>
          )}
          {[2, 3, 4].includes(formik.values.HaveYou) &&
            !formik.values.HospitalisedForExacerbation && (
              <>
                <Text style={styles.optionLabel}>
                  Are you receiving treatment for your COPD flare-up?
                </Text>
                <RadioButtonsGroup
                  value={formik.values["RecievingTreatmentFlareUp"]}
                  setValue={recievingTreatmentFlareUpSelection}
                  style={styles.radiobuttonOption}
                />
              </>
            )}
          {(formik.values.HospitalisedForExacerbation ||
            formik.values.RecievingTreatmentFlareUp ||
            formik.values.HaveYou === 1) && (
            <>
              <Text style={styles.optionLabel}>
                What treatment antibiotics?
              </Text>
              <View style={styles.optionDropdownContainer}>
                <DropDown
                  id="TreatmentAntibiotics"
                  placeholder="Choose Option"
                  isOpen={openDropdown === "TreatmentAntibiotics"}
                  onOpen={(id) => setOpenDropdown(id)}
                  onOptionSelected={(id) => {
                    formik.setFieldValue("AnyOtherAntibiotics", "");
                    formik.setFieldValue("TreatmentAntibiotics", id);
                    setOpenDropdown(null);
                  }}
                  options={ANTIBIOTICS_OPTIONS}
                  zIndex={91}
                  selectedId={formik.values.TreatmentAntibiotics}
                />
              </View>
              {formik.touched.TreatmentAntibiotics &&
                formik.errors.TreatmentAntibiotics && (
                  <Text style={styles.validationMessage}>
                    {formik.errors.TreatmentAntibiotics}
                  </Text>
                )}
              {formik.values.TreatmentAntibiotics === 4 && (
                <TextInput
                  label="Any Other Antibiotics"
                  mode="outlined"
                  value={formik.values.AnyOtherAntibiotics}
                  onChangeText={formik.handleChange("AnyOtherAntibiotics")}
                  outlineStyle={{ borderColor: "#C7C4C4", borderRadius: 14 }}
                  theme={{
                    colors: {
                      primary: "#000",
                      background: "#0D66D00A",
                    },
                  }}
                />
              )}
              <TextInput
                style={styles.userTextInput}
                label="Oral Steroids"
                mode="outlined"
                value={formik.values.TreatmentOralSteroid}
                onChangeText={formik.handleChange("TreatmentOralSteroid")}
                outlineStyle={{ borderColor: "#C7C4C4", borderRadius: 14 }}
                theme={{
                  colors: {
                    primary: "#000",
                    background: "#0D66D00A",
                  },
                }}
              />
              <TextInput
                style={styles.userTextInput}
                label="Others"
                mode="outlined"
                value={formik.values.TreatmentOther}
                onChangeText={formik.handleChange("TreatmentOther")}
                outlineStyle={{ borderColor: "#C7C4C4", borderRadius: 14 }}
                theme={{
                  colors: {
                    primary: "#000",
                    background: "#0D66D00A",
                  },
                }}
              />
            </>
          )}
        </View>
      </ScrollView>
      <Button
        mode="contained"
        labelStyle={{ fontFamily: "Nunito-SemiBold", fontSize: 16 }}
        style={{ marginVertical: 20, marginHorizontal: 20 }}
        onPress={() => formik.handleSubmit()}
      >
        Submit
      </Button>
    </View>
  );
};

export default WellbeingAssessment;

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    justifyContent: "space-between",
    flex: 1,
    paddingVertical: 20,
  },
  optionsContainer: {
    // backgroundColor: "olive",
    // gap: 10,
  },
  optionLabel: {
    // backgroundColor: "orange",
    fontFamily: "Nunito-SemiBold",
    fontSize: 18,
    paddingTop: 15,
    paddingBottom: 10,
    paddingHorizontal: 20,
    color: COLORS.black,
  },
  subOptionContainer: {
    marginHorizontal: 20,
    boxShadow: "0px 0px 10px 0px rgba(0,0,0,0.1)",
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingVertical: 5,
    marginVertical: 15,
  },
  subOptionLabel: {
    fontFamily: "Nunito-Regular",
    fontSize: 16,
    color: COLORS.gray,
  },
  divider: {
    borderBottomWidth: 1,
    borderColor: COLORS.borderGray,
    paddingBottom: 10,
    marginHorizontal: 20,
  },
  optionDropdownContainer: { paddingHorizontal: 20 },
  radiobuttonOption: { paddingHorizontal: 20 },
  userTextInput: { marginHorizontal: 20, marginTop: 10 },
  validationMessage: {
    // backgroundColor: "red",
    fontFamily: "Nunito-SemiBold",
    fontSize: 12,
    color: "#D2042D",
    paddingHorizontal: 20,
    paddingTop: 5,
  },
});
