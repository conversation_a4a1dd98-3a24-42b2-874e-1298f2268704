import DropDown from "@/components/ui/DropDown";
import RadioButtonsGroup from "@/components/ui/RadioButtonsGroup";
import {
  ANTIBIOTICS_OPTIONS,
  BREATHING_TODAY_OPTIONS,
  COUGH_TODAY_OPTIONS,
  DAILY_LIVING_OPTIONS,
  HAVE_YOU_OPTIONS,
  PHYSICAL_ACTIVITY_OPTIONS,
  SPUTUM_COLOR_OPTIONS,
  SPUTUM_OPTIONS,
  SWELLING_OPTIONS,
  TIREDNESS_OPTIONS,
} from "@/constants/strings";
import { wellbeingFormValidation } from "@/validations/schema";
import { useFormik } from "formik";
import React, { useState } from "react";
import { StyleSheet, View } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import { Text, TextInput } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const WellbeingAssessment = () => {
  const [openDropdown, setOpenDropdown] = useState<String | null>();
  const insets = useSafeAreaInsets();
  const formik = useFormik({
    initialValues: {
      BreathingToday: 0,
      BreathingAffectingDailyActivities: 0,
      PhysicalActivity: 0,
      HaveCoughToday: false,
      HowIsCough: 0,
      SputumProducingToday: 0,
      ColorOfSputum: 0,
      FeelTiredToday: false,
      IsYourTiredness: 0,
      AnkleSwelling: false,
      IsAnkleSwelling: 0,
      ChestPainTightness: false,
      Heartburn: false,
      HeartburnWokenAtNight: false,
      FlareUpToday: false,
      HaveYou: 0,
      TreatmentAntibiotics: 0,
      HospitalisedForExacerbation: false,
      RecievingTreatmentFlareUp: false,
      AnyOtherAntibiotics: "",
      TreatmentOralSteroid: "",
      TreatmentOther: "",
      OtherConcern: "",
    },
    onSubmit: () => {},
    validationSchema: wellbeingFormValidation,
  });

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 300 }} // Remove later
      >
        <View style={styles.optionsContainer}>
          <Text variant="titleMedium">How is your breathing today?</Text>
          <DropDown
            id="BreathingToday"
            placeholder="Choose Option"
            isOpen={openDropdown === "BreathingToday"}
            onOpen={(id) => setOpenDropdown(id)}
            onOptionSelected={(id) => {
              formik.setFieldValue("BreathingToday", id);
              setOpenDropdown(null);
            }}
            options={BREATHING_TODAY_OPTIONS}
            zIndex={100}
            selectedId={formik.values.BreathingToday}
          />
          <Text variant="titleMedium">
            How is your breathing affecting your ability to perform?
          </Text>

          <Text variant="bodyLarge">
            Activities of daily living e.g. self-washing/dress, cooking,
            housework.
          </Text>
          <DropDown
            id="BreathingAffectingDailyActivities"
            placeholder="Choose Option"
            isOpen={openDropdown === "BreathingAffectingDailyActivities"}
            onOpen={(id) => setOpenDropdown(id)}
            onOptionSelected={(id) => {
              formik.setFieldValue("BreathingAffectingDailyActivities", id);
              setOpenDropdown(null);
            }}
            options={DAILY_LIVING_OPTIONS}
            zIndex={99}
            selectedId={formik.values.BreathingAffectingDailyActivities}
          />
          <Text variant="bodyLarge">
            Physical activities e.g. walking, shopping, gardening.
          </Text>
          <DropDown
            id="PhysicalActivity"
            placeholder="Choose Option"
            isOpen={openDropdown === "PhysicalActivity"}
            onOpen={(id) => setOpenDropdown(id)}
            onOptionSelected={(id) => {
              formik.setFieldValue("PhysicalActivity", id);
              setOpenDropdown(null);
            }}
            options={PHYSICAL_ACTIVITY_OPTIONS}
            zIndex={98}
            selectedId={formik.values.PhysicalActivity}
          />

          <Text variant="titleMedium">Do you have cough today?</Text>
          <RadioButtonsGroup
            value={formik.values["HaveCoughToday"]}
            setFieldValue={formik.setFieldValue}
            field={"HaveCoughToday"}
          />

          {formik.values.HaveCoughToday && (
            <>
              <Text variant="titleMedium">How is your cough?</Text>
              <DropDown
                id="HowIsCough"
                placeholder="Choose Option"
                isOpen={openDropdown === "HowIsCough"}
                onOpen={(id) => setOpenDropdown(id)}
                onOptionSelected={(id) => {
                  formik.setFieldValue("HowIsCough", id);
                  setOpenDropdown(null);
                }}
                options={COUGH_TODAY_OPTIONS}
                zIndex={97}
                selectedId={formik.values.HowIsCough}
              />
            </>
          )}

          <Text variant="titleMedium">
            How much sputum are your producing today?
          </Text>
          <DropDown
            id="SputumProducingToday"
            placeholder="Choose Option"
            isOpen={openDropdown === "SputumProducingToday"}
            onOpen={(id) => setOpenDropdown(id)}
            onOptionSelected={(id) => {
              formik.setFieldValue("SputumProducingToday", id);
              setOpenDropdown(null);
            }}
            options={SPUTUM_OPTIONS}
            zIndex={96}
            selectedId={formik.values.SputumProducingToday}
          />

          {
            <>
              <Text variant="titleMedium">
                What is the color of your sputum today?
              </Text>
              <DropDown
                id="ColorOfSputum"
                placeholder="Choose Option"
                isOpen={openDropdown === "ColorOfSputum"}
                onOpen={(id) => setOpenDropdown(id)}
                onOptionSelected={(id) => {
                  formik.setFieldValue("ColorOfSputum", id);
                  setOpenDropdown(null);
                }}
                options={SPUTUM_COLOR_OPTIONS}
                zIndex={95}
                selectedId={formik.values.ColorOfSputum}
              />
            </>
          }

          <Text variant="titleMedium">Do you feel tired?</Text>
          <RadioButtonsGroup
            value={formik.values["FeelTiredToday"]}
            setFieldValue={formik.setFieldValue}
            field={"FeelTiredToday"}
          />

          {formik.values.FeelTiredToday && (
            <>
              <Text variant="titleMedium">Is your tiredness?</Text>
              <DropDown
                id="IsYourTiredness"
                placeholder="Choose Option"
                isOpen={openDropdown === "IsYourTiredness"}
                onOpen={(id) => setOpenDropdown(id)}
                onOptionSelected={(id) => {
                  formik.setFieldValue("IsYourTiredness", id);
                  setOpenDropdown(null);
                }}
                options={TIREDNESS_OPTIONS}
                zIndex={94}
                selectedId={formik.values.IsYourTiredness}
              />
            </>
          )}

          <Text variant="titleMedium">Do you have ankle swelling today?</Text>
          <RadioButtonsGroup
            value={formik.values["AnkleSwelling"]}
            setFieldValue={formik.setFieldValue}
            field={"AnkleSwelling"}
          />

          {formik.values.AnkleSwelling && (
            <>
              <Text variant="titleMedium">Is your swelling?</Text>
              <DropDown
                id="IsAnkleSwelling"
                placeholder="Choose Option"
                isOpen={openDropdown === "IsAnkleSwelling"}
                onOpen={(id) => setOpenDropdown(id)}
                onOptionSelected={(id) => {
                  formik.setFieldValue("IsAnkleSwelling", id);
                  setOpenDropdown(null);
                }}
                options={SWELLING_OPTIONS}
                zIndex={93}
                selectedId={formik.values.IsAnkleSwelling}
              />
            </>
          )}

          <Text variant="titleMedium">
            Do you have chest pain/tightness today?
          </Text>
          <RadioButtonsGroup
            value={formik.values["ChestPainTightness"]}
            setFieldValue={formik.setFieldValue}
            field={"ChestPainTightness"}
          />

          <Text variant="titleMedium">
            Have you had any heartburn in the last 24 hours?
          </Text>
          <RadioButtonsGroup
            value={formik.values["Heartburn"]}
            setFieldValue={formik.setFieldValue}
            field={"Heartburn"}
          />

          {formik.values.Heartburn && (
            <>
              <Text variant="titleMedium">
                Has the heartburn woken you up at night?
              </Text>
              <RadioButtonsGroup
                value={formik.values["HeartburnWokenAtNight"]}
                setFieldValue={formik.setFieldValue}
                field={"HeartburnWokenAtNight"}
              />
            </>
          )}

          <Text variant="titleMedium">
            Do you feel you have a flare-up of your COPD today?
          </Text>
          <RadioButtonsGroup
            value={formik.values["FlareUpToday"]}
            setFieldValue={formik.setFieldValue}
            field={"FlareUpToday"}
          />

          {formik.values.FlareUpToday && (
            <>
              <Text variant="titleMedium">Have you?</Text>
              <DropDown
                id="HaveYou"
                placeholder="Choose Option"
                isOpen={openDropdown === "HaveYou"}
                onOpen={(id) => setOpenDropdown(id)}
                onOptionSelected={(id) => {
                  formik.setFieldValue("HaveYou", id);
                  setOpenDropdown(null);
                }}
                options={HAVE_YOU_OPTIONS}
                zIndex={92}
                selectedId={formik.values.HaveYou}
              />
            </>
          )}
          {formik.values.HaveYou === 4 && (
            <>
              <Text variant="titleMedium">
                Have you had to be hospitalised for your exacerbation?
              </Text>
              <RadioButtonsGroup
                value={formik.values["HospitalisedForExacerbation"]}
                setFieldValue={formik.setFieldValue}
                field={"HospitalisedForExacerbation"}
              />
            </>
          )}
          {[2, 3, 4].includes(formik.values.HaveYou) &&
            !formik.values.HospitalisedForExacerbation && (
              <>
                <Text variant="titleMedium">
                  Are you receiving treatment for your COPD flare-up?
                </Text>
                <RadioButtonsGroup
                  value={formik.values["RecievingTreatmentFlareUp"]}
                  setFieldValue={formik.setFieldValue}
                  field={"RecievingTreatmentFlareUp"}
                />
              </>
            )}
          {(formik.values.HospitalisedForExacerbation ||
            formik.values.RecievingTreatmentFlareUp ||
            formik.values.HaveYou === 1) && (
            <>
              <Text variant="titleMedium">What treatment antibiotics?</Text>
              <DropDown
                id="TreatmentAntibiotics"
                placeholder="Choose Option"
                isOpen={openDropdown === "TreatmentAntibiotics"}
                onOpen={(id) => setOpenDropdown(id)}
                onOptionSelected={(id) => {
                  formik.setFieldValue("TreatmentAntibiotics", id);
                  setOpenDropdown(null);
                }}
                options={ANTIBIOTICS_OPTIONS}
                zIndex={91}
                selectedId={formik.values.TreatmentAntibiotics}
              />
              <TextInput
                label="Oral Steroids"
                mode="outlined"
                onChangeText={formik.handleChange("TreatmentOralSteroid")}
              />
              <TextInput
                label="Others"
                mode="outlined"
                onChangeText={formik.handleChange("TreatmentOther")}
              />
            </>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

export default WellbeingAssessment;

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    justifyContent: "space-between",
    flex: 1,
    padding: 20,
  },
  optionsContainer: {
    gap: 10,
  },
});
