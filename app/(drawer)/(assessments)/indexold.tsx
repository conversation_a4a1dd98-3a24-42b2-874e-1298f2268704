import ElevatedSurfaceCard from "@/components/ui/ElevatedSurfaceCard";
import { router } from "expo-router";
import React from "react";
import { StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";
import AssessmentRecordsImage from "../../../assets/images/png/assesmentRecords.png";
import AddAssessmentImage from "../../../assets/images/png/newAssessment.png";

const navigateToScreen = (pathName: any) => {
  router.push(pathName);
};

const MyAssessments = () => {
  return (
    <View style={{ flex: 1, alignItems: "center", gap: 20, padding: 20 }}>
      <ElevatedSurfaceCard
        media={AddAssessmentImage}
        title={"Add New Assessment"}
        description={`By tapping will allow you${"\n"} to do a quick check on your condition.`}
        handlePress={() =>
          navigateToScreen("/(drawer)/(assessments)/newAssessment")
        }
      />
      <ElevatedSurfaceCard
        media={AssessmentRecordsImage}
        title={"My Assessment Records"}
        description={`By tapping will bring up${"\n"} all your previous assessment records.`}
        handlePress={() => navigateToScreen("/my-assessments")}
      />
      <Text style={styles.userNotice}>
        You have not done any Assessment. We{"\n"} engorage you to do your
        wellbeing{"\n"} assessment every day.
      </Text>
    </View>
  );
};

export default MyAssessments;

const styles = StyleSheet.create({
  userNotice: {
    textAlign: "center",
  },
});
