import ScreenLabel from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import { api } from "@/network/api";
import { ASSESSMENTS_LIST } from "@/network/apiConstants";
import { useLoaderStore } from "@/store/loader.store";
import { getDateString } from "@/util";
import { Link } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  Pressable,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { Icon, TouchableRipple } from "react-native-paper";

const _COLORS = {
  stable: "#1DB954",
  prodrome: "#FFBF00",
  exacerbation: "#ED4848",
} as const;

type StatusName = keyof typeof _COLORS;

function getStatusStringAndColor(statusName: string) {
  const key = statusName.toLowerCase() as StatusName;
  const color = _COLORS[key] ?? COLORS.black;
  const backgroundColor = color + "1A";

  return {
    backgroundColor,
    textColor: color,
  };
}
const AssessmentListItem = ({ item }: { item: Assessment }) => (
  <View
    style={{
      flexDirection: "row",
    }}
  >
    <View
      style={{
        width: "20%",
        justifyContent: "center",
        alignItems: "flex-start",
      }}
    >
      <Text
        style={{
          textAlign: "center",
          color: COLORS.gray,
          fontFamily: "Nunito-Medium",
          fontSize: 16,
        }}
      >
        {/* {item.CreatedDate.split("T")[0]} */}
        {/* 07 May{"\n"}2023 */}
        {getDateString(item.CreatedDate, true)}
      </Text>
    </View>

    <View
      style={{
        flex: 1,
        backgroundColor: COLORS.cardBg,
        padding: 14,
        borderRadius: 14,
        gap: 8,
      }}
    >
      <Text
        style={{
          color: COLORS.gray,
          fontFamily: "Nunito-SemiBold",
          fontSize: 16,
        }}
        numberOfLines={1}
        lineBreakMode="tail"
      >
        Performed by :
        <Text
          style={{
            color: COLORS.black,
          }}
        >
          {item.CreatedByName}
        </Text>
      </Text>
      <Text
        style={{
          color: COLORS.gray,
          fontFamily: "Nunito-SemiBold",
          fontSize: 16,
        }}
      >
        Type :
        <Text style={{ color: COLORS.black }}> {item.AssessmentType}</Text>
      </Text>
      <View
        style={{
          flexDirection: "row",
          gap: 18,
          marginTop: 6,
          marginBottom: 6,
        }}
      >
        <Text
          style={{
            width: 96,
            textAlign: "center",
            fontFamily: "Nunito-SemiBold",
            fontSize: 14,
            backgroundColor: getStatusStringAndColor(item.ReportStatusName)
              .backgroundColor,
            paddingVertical: 6,
            // paddingHorizontal: 12,
            borderRadius: 99,
            color: getStatusStringAndColor(item.ReportStatusName).textColor,
          }}
        >
          {item.ReportStatusName === "Exacerbation"
            ? "Unstable"
            : item.ReportStatusName}
        </Text>
        <TouchableRipple
          style={{
            flex: 1,
            borderRadius: 99,
            backgroundColor: COLORS.blue,
            // paddingHorizontal: 12,
            paddingVertical: 6,
          }}
          rippleColor={COLORS.rippleColor}
          onPress={() => console.log("View Report")}
        >
          <Text
            style={{
              color: COLORS.white,
              textAlign: "center",
              fontFamily: "Nunito-SemiBold",
              fontSize: 14,
            }}
          >
            View Details
          </Text>
        </TouchableRipple>
      </View>
    </View>
  </View>
);
interface Assessment {
  Id: number;
  AssessmentType: string;
  CreatedDate: string;
  CreatedByName: string;
  ReportStatus: number;
  ReportStatusName: string;
}

const MyAssessments = () => {
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [pageNo, setPageNo] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // console.log("assessments", assessments);
  const setIsLoaderVisible = useLoaderStore(
    (state) => state.setIsLoaderVisible
  );

  const noOfRowsPerPage = 10; // Adjust based on your needs

  const fetchAssessments = useCallback(async (page = 1, reset = false) => {
    try {
      const response = await api.get(
        `${ASSESSMENTS_LIST}?pageNo=${page}&noOfRowsPerPage=${noOfRowsPerPage}`
      );

      if (response.data.Status !== 1) {
        throw new Error(response.data.Message || "API call failed");
      }

      const newData = response.data.Data || [];
      const totalCount = response.data.TotalCount || 0;

      if (reset) {
        setAssessments(newData);
        setHasMore(newData.length < totalCount);
      } else {
        let updatedDataLength = 0;
        setAssessments((prevData) => {
          // Prevent duplicates by filtering out items that already exist
          const existingIds = new Set(prevData.map((item) => item.Id));
          const uniqueNewData = newData.filter(
            (item: Assessment) => !existingIds.has(item.Id)
          );
          const updatedData = [...prevData, ...uniqueNewData];
          updatedDataLength = updatedData.length;
          return updatedData;
        });

        // Update hasMore state based on the final data length
        setHasMore(updatedDataLength < totalCount);
      }

      setError(null);

      console.log(
        `Loaded page ${page}, total items loaded: ${reset ? newData.length : "prev + " + newData.length}/${totalCount}`
      );
    } catch (error) {
      console.error("Error fetching assessments:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    }
  }, []);

  // Initial load
  useEffect(() => {
    const fetchInitialData = async () => {
      // setLoading(true);
      setIsLoaderVisible(true);
      try {
        await fetchAssessments(1, true);
      } catch (error) {
        console.error("Error loading initial data:", error);
      } finally {
        setLoading(false);
        setIsLoaderVisible(false);
      }
    };

    fetchInitialData();
  }, [fetchAssessments]);

  // Load more data
  const loadMoreData = useCallback(async () => {
    if (loadingMore || !hasMore || loading) return;
    console.log("Loading more data...");

    setLoadingMore(true);
    const nextPage = pageNo + 1;

    // Update page number immediately to prevent race conditions
    setPageNo(nextPage);

    try {
      await fetchAssessments(nextPage, false);
    } catch (error) {
      // Revert page number on error
      setPageNo(pageNo);
      console.error("Error loading more data:", error);
    } finally {
      setLoadingMore(false);
    }
  }, [pageNo, hasMore, loadingMore, loading, fetchAssessments]);

  // Refresh function
  const onRefresh = useCallback(async () => {
    setLoading(true);
    // setIsLoaderVisible(true);
    setPageNo(1);
    setHasMore(true);
    setError(null);

    try {
      await fetchAssessments(1, true);
    } catch (error) {
      console.error("Error refreshing data:", error);
    } finally {
      setLoading(false);
      setIsLoaderVisible(false);
    }
  }, [fetchAssessments]);

  // Render footer loading indicator
  const renderFooter = () => {
    if (!loadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={COLORS.blue} />
        <Text style={styles.loadingText}>Loading more assessments...</Text>
      </View>
    );
  };
  // Render empty state
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>
        {error ? `Error: ${error}` : "No assessments available"}
      </Text>
    </View>
  );
  useEffect(() => {
    return () => {
      console.log("unmounting");
    };
  }, []);
  if (assessments.length === 0) return null;

  return (
    <View style={styles.container}>
      <ScreenLabel
        label="My Assessments"
        style={{ paddingLeft: SCREEN_PADDING * 2, marginBottom: 20 }}
      />
      <FlatList
        data={assessments}
        renderItem={AssessmentListItem}
        keyExtractor={(item) => item.Id.toString()}
        onEndReached={loadMoreData}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        // ListEmptyComponent={renderEmpty}
        refreshing={loading}
        onRefresh={onRefresh}
        removeClippedSubviews={true}
        maxToRenderPerBatch={6}
        windowSize={6}
        initialNumToRender={noOfRowsPerPage}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          styles.listContainer,
          assessments.length === 0 && styles.emptyContainer,
        ]}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
      <Link href="/(drawer)/(assessments)/newAssessment" asChild>
        <Pressable
          // onPress={() => router.push("/(drawer)/(assessments)/newAssessment")}
          style={{
            position: "absolute",
            bottom: 20,
            right: 20,
            backgroundColor: COLORS.blue,
            width: 60,
            height: 60,
            borderRadius: 30,
            alignItems: "center",
            justifyContent: "center",
            boxShadow: "0 2px 4px rgba(0, 0, 0, 0.25)",
          }}
        >
          <Icon source="plus" size={32} color={COLORS.white} />
        </Pressable>
      </Link>
    </View>
  );
};

export default MyAssessments;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  listContainer: {
    paddingRight: SCREEN_PADDING * 2,
    paddingLeft: SCREEN_PADDING * 2,
  },

  separator: {
    height: 20,
  },
  footerLoader: {
    padding: 20,
    alignItems: "center",
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: "#666",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
});
