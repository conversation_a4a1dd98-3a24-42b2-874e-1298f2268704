import { SCREEN_PADDING } from "@/constants";
import { api } from "@/network/api";
import { ASSESSMENTS_LIST } from "@/network/apiConstants";
import { useLoaderStore } from "@/store/loader.store";
import React, { useCallback, useEffect, useState } from "react";
import { ActivityIndicator, FlatList, StyleSheet, View } from "react-native";
import { Text, TouchableRipple } from "react-native-paper";

const COLORS = {
  stable: "#8FB339",
  prodrome: "orange",
  exacerbation: "red",
} as const;

type StatusName = keyof typeof COLORS;

function getStatusStringAndColor(statusName: string) {
  const key = statusName.toLowerCase() as StatusName;
  return {
    color: COLORS[key] ?? "black",
  };
}

interface Assessment {
  Id: number;
  AssessmentType: string;
  CreatedDate: string;
  CreatedByName: string;
  ReportStatus: number;
  ReportStatusName: string;
}

const MyAssessments = () => {
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [pageNo, setPageNo] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  console.log("assessments", assessments);

  const setIsLoaderVisible = useLoaderStore(
    (state) => state.setIsLoaderVisible
  );

  const noOfRowsPerPage = 10; // Adjust based on your needs

  const fetchAssessments = useCallback(async (page = 1, reset = false) => {
    try {
      const response = await api.get(
        `${ASSESSMENTS_LIST}?pageNo=${page}&noOfRowsPerPage=${noOfRowsPerPage}`
      );

      if (response.data.Status !== 1) {
        throw new Error(response.data.Message || "API call failed");
      }

      const newData = response.data.Data || [];
      const totalCount = response.data.TotalCount || 0;

      if (reset) {
        setAssessments(newData);
        setHasMore(newData.length < totalCount);
      } else {
        let updatedDataLength = 0;
        setAssessments((prevData) => {
          // Prevent duplicates by filtering out items that already exist
          const existingIds = new Set(prevData.map((item) => item.Id));
          const uniqueNewData = newData.filter(
            (item: Assessment) => !existingIds.has(item.Id)
          );
          const updatedData = [...prevData, ...uniqueNewData];
          updatedDataLength = updatedData.length;
          return updatedData;
        });

        // Update hasMore state based on the final data length
        setHasMore(updatedDataLength < totalCount);
      }

      setError(null);

      console.log(
        `Loaded page ${page}, total items loaded: ${reset ? newData.length : "prev + " + newData.length}/${totalCount}`
      );
    } catch (error) {
      console.error("Error fetching assessments:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    }
  }, []);

  // Initial load
  useEffect(() => {
    const fetchInitialData = async () => {
      // setLoading(true);
      setIsLoaderVisible(true);
      try {
        await fetchAssessments(1, true);
      } catch (error) {
        console.error("Error loading initial data:", error);
      } finally {
        setLoading(false);
        setIsLoaderVisible(false);
      }
    };

    fetchInitialData();
  }, [fetchAssessments]);

  // Load more data
  const loadMoreData = useCallback(async () => {
    if (loadingMore || !hasMore || loading) return;

    setLoadingMore(true);
    const nextPage = pageNo + 1;

    // Update page number immediately to prevent race conditions
    setPageNo(nextPage);

    try {
      await fetchAssessments(nextPage, false);
    } catch (error) {
      // Revert page number on error
      setPageNo(pageNo);
      console.error("Error loading more data:", error);
    } finally {
      setLoadingMore(false);
    }
  }, [pageNo, hasMore, loadingMore, loading, fetchAssessments]);

  // Refresh function
  const onRefresh = useCallback(async () => {
    setLoading(true);
    // setIsLoaderVisible(true);
    setPageNo(1);
    setHasMore(true);
    setError(null);

    try {
      await fetchAssessments(1, true);
    } catch (error) {
      console.error("Error refreshing data:", error);
    } finally {
      setLoading(false);
      setIsLoaderVisible(false);
    }
  }, [fetchAssessments]);

  // Render individual assessment item
  const renderAssessment = ({ item }: { item: Assessment }) => (
    <TouchableRipple
      rippleColor={"rgba(0, 0, 0, .32)"}
      key={item.Id}
      style={styles.assessmentCard}
    >
      <View style={styles.assessmentContent}>
        <View>
          <Text style={styles.labelText}>Performed date</Text>
          <Text style={styles.valueText}>{item.CreatedDate.split("T")[0]}</Text>
          <Text style={styles.labelText}>Type</Text>
          <Text style={styles.valueText}>
            {item?.AssessmentType + " " + item.Id || "N/A"}
          </Text>
        </View>
        <View>
          <Text style={styles.labelText}>Performed by</Text>
          <Text style={styles.valueText}>{item.CreatedByName}</Text>
          <Text style={styles.labelText}>Report Status</Text>
          <View style={styles.statusContainer}>
            <View
              style={[
                styles.statusIndicator,
                {
                  backgroundColor: getStatusStringAndColor(
                    item.ReportStatusName
                  ).color,
                },
              ]}
            />
            <Text style={styles.valueText}>{item.ReportStatusName}</Text>
          </View>
        </View>
      </View>
    </TouchableRipple>
  );

  // Render footer loading indicator
  const renderFooter = () => {
    if (!loadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#8FB339" />
        <Text style={styles.loadingText}>Loading more assessments...</Text>
      </View>
    );
  };

  // Render empty state
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>
        {error ? `Error: ${error}` : "No assessments available"}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={assessments}
        renderItem={renderAssessment}
        keyExtractor={(item) => item.Id.toString()}
        onEndReached={loadMoreData}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        refreshing={loading}
        onRefresh={onRefresh}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={noOfRowsPerPage}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          styles.listContainer,
          assessments.length === 0 && styles.emptyContainer,
        ]}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
};

export default MyAssessments;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  listContainer: {
    padding: SCREEN_PADDING * 2,
  },
  assessmentCard: {
    padding: 16,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  assessmentContent: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  labelText: {
    fontSize: 14,
    color: "#888",
    marginBottom: 4,
  },
  valueText: {
    fontSize: 16,
    color: "black",
  },
  statusContainer: {
    flexDirection: "row",
    gap: 5,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  separator: {
    height: 10,
  },
  footerLoader: {
    padding: 20,
    alignItems: "center",
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: "#666",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
});
