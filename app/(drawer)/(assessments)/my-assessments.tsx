import { SCREEN_PADDING } from "@/constants";
import { api } from "@/network/api";
import { ASSESSMENTS_LIST } from "@/network/apiConstants";
import { useLoaderStore } from "@/store/loader.store";
import React, { useCallback, useEffect, useState } from "react";
import { ActivityIndicator, FlatList, StyleSheet, View } from "react-native";
import { Text, TouchableRipple } from "react-native-paper";

const getDateString = (date: string, breakLine: boolean = false) => {
  {
    /* 07 May{"\n"}2023 */
  } // in this formate
  const [year, month, day] = date.split("T")[0].split("-");
  const monthName = new Date(`${year}-${month}-01`).toLocaleString("en-US", {
    month: "short",
  });
  if (breakLine) {
    return `${day} ${monthName}\n ${year}`;
  }
  return `${day} ${monthName} ${year}`;
};

const COLORS = {
  stable: "#1DB954",
  prodrome: "#FFBF00",
  exacerbation: "#ED4848",
} as const;

type StatusName = keyof typeof COLORS;

function getStatusStringAndColor(statusName: string) {
  const key = statusName.toLowerCase() as StatusName;
  const color = COLORS[key] ?? "#000000";
  const backgroundColor = color + "1A";

  return {
    backgroundColor,
    textColor: color,
  };
}

interface Assessment {
  Id: number;
  AssessmentType: string;
  CreatedDate: string;
  CreatedByName: string;
  ReportStatus: number;
  ReportStatusName: string;
}

const MyAssessments = () => {
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [pageNo, setPageNo] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // console.log("assessments", assessments);

  const setIsLoaderVisible = useLoaderStore(
    (state) => state.setIsLoaderVisible
  );

  const noOfRowsPerPage = 10; // Adjust based on your needs

  const fetchAssessments = useCallback(async (page = 1, reset = false) => {
    try {
      const response = await api.get(
        `${ASSESSMENTS_LIST}?pageNo=${page}&noOfRowsPerPage=${noOfRowsPerPage}`
      );

      if (response.data.Status !== 1) {
        throw new Error(response.data.Message || "API call failed");
      }

      const newData = response.data.Data || [];
      const totalCount = response.data.TotalCount || 0;

      if (reset) {
        setAssessments(newData);
        setHasMore(newData.length < totalCount);
      } else {
        let updatedDataLength = 0;
        setAssessments((prevData) => {
          // Prevent duplicates by filtering out items that already exist
          const existingIds = new Set(prevData.map((item) => item.Id));
          const uniqueNewData = newData.filter(
            (item: Assessment) => !existingIds.has(item.Id)
          );
          const updatedData = [...prevData, ...uniqueNewData];
          updatedDataLength = updatedData.length;
          return updatedData;
        });

        // Update hasMore state based on the final data length
        setHasMore(updatedDataLength < totalCount);
      }

      setError(null);

      console.log(
        `Loaded page ${page}, total items loaded: ${reset ? newData.length : "prev + " + newData.length}/${totalCount}`
      );
    } catch (error) {
      console.error("Error fetching assessments:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    }
  }, []);

  // Initial load
  useEffect(() => {
    const fetchInitialData = async () => {
      // setLoading(true);
      setIsLoaderVisible(true);
      try {
        await fetchAssessments(1, true);
      } catch (error) {
        console.error("Error loading initial data:", error);
      } finally {
        setLoading(false);
        setIsLoaderVisible(false);
      }
    };

    fetchInitialData();
  }, [fetchAssessments]);

  // Load more data
  const loadMoreData = useCallback(async () => {
    if (loadingMore || !hasMore || loading) return;

    setLoadingMore(true);
    const nextPage = pageNo + 1;

    // Update page number immediately to prevent race conditions
    setPageNo(nextPage);

    try {
      await fetchAssessments(nextPage, false);
    } catch (error) {
      // Revert page number on error
      setPageNo(pageNo);
      console.error("Error loading more data:", error);
    } finally {
      setLoadingMore(false);
    }
  }, [pageNo, hasMore, loadingMore, loading, fetchAssessments]);

  // Refresh function
  const onRefresh = useCallback(async () => {
    setLoading(true);
    // setIsLoaderVisible(true);
    setPageNo(1);
    setHasMore(true);
    setError(null);

    try {
      await fetchAssessments(1, true);
    } catch (error) {
      console.error("Error refreshing data:", error);
    } finally {
      setLoading(false);
      setIsLoaderVisible(false);
    }
  }, [fetchAssessments]);

  const AssessmentListItem = ({ item }: { item: Assessment }) => (
    <View
      style={{
        flexDirection: "row",
      }}
    >
      <View
        style={{ width: "25%", justifyContent: "center", alignItems: "center" }}
      >
        <Text
          variant="bodyMedium"
          style={{ textAlign: "center", color: "#797b7c" }}
        >
          {/* {item.CreatedDate.split("T")[0]} */}
          {/* 07 May{"\n"}2023 */}
          {getDateString(item.CreatedDate, true)}
        </Text>
      </View>

      <View
        style={{
          flex: 1,
          backgroundColor: "rgba(13,102,208,0.06)",
          padding: 14,
          borderRadius: 16,
          gap: 8,
        }}
      >
        <Text
          variant="bodyLarge"
          style={{ color: "#797b7c" }}
          numberOfLines={1}
          lineBreakMode="tail"
        >
          Performed by :
          <Text variant="bodyLarge" style={{ color: "#111" }}>
            {" "}
            {item.CreatedByName}
          </Text>
        </Text>
        <Text variant="bodyLarge" style={{ color: "#797b7c" }}>
          Type :
          <Text variant="bodyLarge" style={{ color: "#111" }}>
            {" "}
            {item.AssessmentType}
          </Text>
        </Text>
        <View
          style={{
            flexDirection: "row",
            gap: 18,
            marginTop: 6,
            marginBottom: 6,
          }}
        >
          <Text
            variant="bodyMedium"
            style={{
              backgroundColor: getStatusStringAndColor(item.ReportStatusName)
                .backgroundColor,
              paddingVertical: 6,
              paddingHorizontal: 12,
              borderRadius: 99,
              color: getStatusStringAndColor(item.ReportStatusName).textColor,
            }}
          >
            {item.ReportStatusName}
          </Text>
          <TouchableRipple
            style={{
              borderRadius: 99,
              backgroundColor: "#306CFE",
              paddingHorizontal: 12,
              paddingVertical: 6,
            }}
            rippleColor={"rgba(0, 0, 0, .32)"}
            onPress={() => console.log("View Report")}
          >
            <Text variant="bodyMedium" style={{ color: "#fff" }}>
              View Details
            </Text>
          </TouchableRipple>
        </View>
      </View>
    </View>
  );

  // Render footer loading indicator
  const renderFooter = () => {
    if (!loadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#8FB339" />
        <Text style={styles.loadingText}>Loading more assessments...</Text>
      </View>
    );
  };

  // Render empty state
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>
        {error ? `Error: ${error}` : "No assessments available"}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={assessments}
        renderItem={AssessmentListItem}
        keyExtractor={(item) => item.Id.toString()}
        onEndReached={loadMoreData}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        refreshing={loading}
        onRefresh={onRefresh}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={noOfRowsPerPage}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          styles.listContainer,
          assessments.length === 0 && styles.emptyContainer,
        ]}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
};

export default MyAssessments;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  listContainer: {
    paddingRight: SCREEN_PADDING * 2,
    paddingLeft: SCREEN_PADDING,
  },
  assessmentCard: {
    padding: 16,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  assessmentContent: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  labelText: {
    fontSize: 14,
    color: "#888",
    marginBottom: 4,
  },
  valueText: {
    fontSize: 16,
    color: "black",
  },
  statusContainer: {
    flexDirection: "row",
    gap: 5,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  separator: {
    height: 20,
  },
  footerLoader: {
    padding: 20,
    alignItems: "center",
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: "#666",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
});
