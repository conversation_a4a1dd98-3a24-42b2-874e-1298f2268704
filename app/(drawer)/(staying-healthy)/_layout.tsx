import Header from "@/components/ui/Header";
import { Stack } from "expo-router";
import React from "react";
import { StyleSheet } from "react-native";

const _MyStayingHealthyLayout = () => {
  return (
    <Stack
      screenOptions={{
        header: (props) => <Header title="My Staying Healthy" {...props} />,
        title: "My Staying Healthy",
        headerShadowVisible: false,
      }}
    />
  );
};

export default _MyStayingHealthyLayout;

const styles = StyleSheet.create({});
