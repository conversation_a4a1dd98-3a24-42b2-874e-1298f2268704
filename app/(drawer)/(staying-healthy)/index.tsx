import LabelUnderline from "@/components/ui/LabelUnderline";
import ScreenLabel from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import { useStayingHealthyStore } from "@/store/stayinghealthy.store";
import { Link } from "expo-router";
import React from "react";
import { Pressable, ScrollView, StyleSheet, Text, View } from "react-native";
import { Icon } from "react-native-paper";

const MyStayingHealthy = () => {
  const stayingHealthyCards = useStayingHealthyStore(
    (state) => state.stayingHealthyCards
  );
  return (
    <ScrollView
      style={{ flex: 1 }}
      contentContainerStyle={{
        padding: SCREEN_PADDING * 2,
        paddingTop: 0,
        backgroundColor: COLORS.white,
        flexGrow: 1,
      }}
    >
      <ScreenLabel label="My Staying Healthy" style={{ marginBottom: 0 }} />
      <LabelUnderline label="Stay Informed:" />
      {stayingHealthyCards.map((item) => (
        <Link href={`/${item.id}`} asChild key={item.id}>
          <Pressable
            style={{
              marginBottom: 16,
              padding: 20,
              borderRadius: 14,
              backgroundColor: COLORS.cardBg,
              flexDirection: "row",
              alignItems: "center",
              gap: 10,
            }}
          >
            <View style={{ flex: 1, gap: 8 }}>
              <Text
                style={{
                  color: COLORS.black,
                  fontFamily: item.isBiggerLabel
                    ? "Nunito-Medium"
                    : "Nunito-SemiBold",

                  fontSize: item.isBiggerLabel ? 22 : 16,
                }}
              >
                {item.label}
              </Text>
              {item.description && (
                <Text
                  style={{
                    color: COLORS.gray,
                    fontFamily: "Nunito-Medium",
                    fontSize: 14,
                    lineHeight: 16,
                  }}
                >
                  {item.description}
                </Text>
              )}
            </View>
            <View style={{}}>
              <Icon source={"arrow-right"} size={24} color={COLORS.blue} />
            </View>
          </Pressable>
        </Link>
      ))}
    </ScrollView>
  );
};

export default MyStayingHealthy;

const styles = StyleSheet.create({});
