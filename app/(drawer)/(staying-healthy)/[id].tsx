import DataNotFound from "@/components/ui/DataNotFound";
import ScreenLabel from "@/components/ui/ScreenLabel";
import { COLORS, SCREEN_PADDING } from "@/constants";
import { useStayingHealthyStore } from "@/store/stayinghealthy.store";
import { useLocalSearchParams } from "expo-router";
import React from "react";
import { ScrollView, StyleSheet, Text, View } from "react-native";

const StayingHealthyDetails = () => {
  const { id } = useLocalSearchParams();
  const getStayingHealthyCardById = useStayingHealthyStore(
    (state) => state.getStayingHealthyCardById
  );
  const stayingHealthyCard = getStayingHealthyCardById(Number(id));
  console.log(stayingHealthyCard, "stayingHealthyCard");
  if (!stayingHealthyCard.details)
    return <DataNotFound message="Details Not Found" />;

  return (
    <ScrollView
      style={{ flex: 1, backgroundColor: "#fff" }}
      contentContainerStyle={{
        padding: SCREEN_PADDING * 2,
        paddingTop: 0,
        backgroundColor: "#fff",
        flexGrow: 1,
      }}
    >
      <ScreenLabel label="Stay Informed" style={{ marginBottom: 20 }} />
      <View
        style={{
          backgroundColor: COLORS.cardBg,
          borderRadius: 14,
          padding: 20,
        }}
      >
        <Text
          style={{
            color: COLORS.black,
            fontFamily: "Nunito-SemiBold",
            fontSize: 16,
          }}
        >
          {stayingHealthyCard.details.title}
        </Text>
        <Text
          style={{
            color: COLORS.gray,
            fontFamily: "Nunito-Medium",
            fontSize: 14,
            lineHeight: 18,
            marginTop: 8,
          }}
        >
          {stayingHealthyCard.details.content}
        </Text>
      </View>
      {/* LINKS */}
      {stayingHealthyCard.details.links.map((link: any) => (
        <View
          key={link.label}
          style={{
            backgroundColor: COLORS.cardBg,
            borderRadius: 14,
            padding: 20,
            marginTop: 20,
          }}
        >
          <Text
            style={{
              color: COLORS.black,
              fontFamily: "Nunito-SemiBold",
              fontSize: 16,
              lineHeight: 20,
            }}
          >
            {link.label}
          </Text>
        </View>
      ))}
    </ScrollView>
  );
};

export default StayingHealthyDetails;

const styles = StyleSheet.create({});
