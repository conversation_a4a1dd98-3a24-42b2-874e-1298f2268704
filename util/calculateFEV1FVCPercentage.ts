import AsyncStorage from "@react-native-async-storage/async-storage";
type userData = { Fev1: number; FVC: number; Height: number; Age: number };
type userDetails = { Data: userData };

export const getFev1Percentage = async (userInfo: userDetails) => {
  if (!userInfo) return;

  let value = 0;
  const mMspFev1 = userInfo!.Data.Fev1; // Set the value later

  const getUserDetails: any = await AsyncStorage.getItem("UserDetails");
  const userDetails = JSON.parse(getUserDetails);

  if (userDetails?.Gender === 1) {
    switch (userDetails.EthniCityId) {
      case 1:
        value = Math.exp(
          -10.342 +
            2.2196 * Math.log(userInfo.Data.Height) +
            0.0574 * Math.log(userInfo.Data.Age) +
            mMspFev1
        );
        break;
      case 2:
        value = Math.exp(
          -10.342 +
            2.2196 * Math.log(userInfo.Data.Height) +
            0.0574 * Math.log(userInfo.Data.Age) +
            -0.1589 +
            mMspFev1
        );
        break;
      case 3:
        value = Math.exp(
          -10.342 +
            2.2196 * Math.log(userInfo.Data.Height) +
            0.0574 * Math.log(userInfo.Data.Age) +
            -0.0351 +
            mMspFev1
        );
        break;
      case 4:
        value = Math.exp(
          -10.342 +
            2.2196 * Math.log(userInfo.Data.Height) +
            0.0574 * Math.log(userInfo.Data.Age) +
            -0.0881 +
            mMspFev1
        );
        break;
      case 5:
        value = Math.exp(
          -10.342 +
            2.2196 * Math.log(userInfo.Data.Height) +
            0.0574 * Math.log(userInfo.Data.Age) +
            -0.0708 +
            mMspFev1
        );
        break;
    }
  } else {
    {
      switch (userDetails.EthniCityId) {
        case 1:
          value = Math.exp(
            -9.6987 +
              2.1211 * Math.log(userInfo.Data.Height) +
              -0.027 * Math.log(userInfo.Data.Age) +
              mMspFev1
          );
          break;
        case 2:
          value = Math.exp(
            -9.6987 +
              2.1211 * Math.log(userInfo.Data.Height) +
              -0.027 * Math.log(userInfo.Data.Age) +
              -0.1484 +
              mMspFev1
          );
          break;
        case 3:
          value = Math.exp(
            -9.6987 +
              2.1211 * Math.log(userInfo.Data.Height) +
              -0.027 * Math.log(userInfo.Data.Age) +
              -0.0149 +
              mMspFev1
          );
          break;
        case 4:
          value = Math.exp(
            -9.6987 +
              2.1211 * Math.log(userInfo.Data.Height) +
              -0.027 * Math.log(userInfo.Data.Age) +
              -0.1208 +
              mMspFev1
          );
          break;
        case 5:
          value = Math.exp(
            -9.6987 +
              2.1211 * Math.log(userInfo.Data.Height) +
              -0.027 * Math.log(userInfo.Data.Age) +
              -0.0708 +
              mMspFev1
          );
          break;
      }
    }
  }

  return value;
};

export const getFVCPercentage = async (userInfo: userDetails) => {
  if (!userInfo) return;
  let value = 0;
  const mMspFvc = userInfo!.Data.FVC; // Set the value later

  const getUserDetails: any = await AsyncStorage.getItem("UserDetails");
  const userDetails = JSON.parse(getUserDetails);

  if (userDetails?.Gender === 1) {
    switch (userDetails.EthniCityId) {
      case 1:
        value = Math.exp(
          -11.2281 +
            2.4135 * Math.log(userInfo.Data.Height) +
            0.0865 * Math.log(userInfo.Data.Age) +
            mMspFvc
        );
        break;
      case 2:
        value = Math.exp(
          -11.2281 +
            2.4135 * Math.log(userInfo.Data.Height) +
            0.0865 * Math.log(userInfo.Data.Age) +
            -0.1684 +
            mMspFvc
        );
        break;
      case 3:
        value = Math.exp(
          -11.2281 +
            2.4135 * Math.log(userInfo.Data.Height) +
            0.0865 * Math.log(userInfo.Data.Age) +
            -0.0405 +
            mMspFvc
        );
        break;
      case 4:
        value = Math.exp(
          -11.2281 +
            2.4135 * Math.log(userInfo.Data.Height) +
            0.0865 * Math.log(userInfo.Data.Age) +
            -0.1177 +
            mMspFvc
        );
        break;
      case 5:
        value = Math.exp(
          -11.2281 +
            2.4135 * Math.log(userInfo.Data.Height) +
            0.0865 * Math.log(userInfo.Data.Age) +
            -0.0825 +
            mMspFvc
        );
        break;
    }
  } else {
    {
      switch (userDetails.EthniCityId) {
        case 1:
          value = Math.exp(
            -10.403 +
              2.2633 * Math.log(userInfo.Data.Height) +
              0.0234 * Math.log(userInfo.Data.Age) +
              mMspFvc
          );
          break;
        case 2:
          value = Math.exp(
            -10.403 +
              2.2633 * Math.log(userInfo.Data.Height) +
              0.0234 * Math.log(userInfo.Data.Age) +
              -0.1555 +
              mMspFvc
          );
          break;
        case 3:
          value = Math.exp(
            -10.403 +
              2.2633 * Math.log(userInfo.Data.Height) +
              0.0234 * Math.log(userInfo.Data.Age) +
              -0.0262 +
              mMspFvc
          );
          break;
        case 4:
          value = Math.exp(
            -10.403 +
              2.2633 * Math.log(userInfo.Data.Height) +
              0.0234 * Math.log(userInfo.Data.Age) +
              -0.1516 +
              mMspFvc
          );
          break;
        case 5:
          value = Math.exp(
            -10.403 +
              2.2633 * Math.log(userInfo.Data.Height) +
              0.0234 * Math.log(userInfo.Data.Age) +
              -0.0833 +
              mMspFvc
          );
          break;
      }
    }
  }

  return value;
};
