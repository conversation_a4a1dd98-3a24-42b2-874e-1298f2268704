import * as Yup from "yup";

export const spirometryFormValidation = Yup.object({
  FEV1: Yup.number().required().max(15),
  FVC: Yup.number().required().max(15),
});

const testForDropdownNoOptionSelected = {
  label: "not-zero",
  errorMessage: "Please select an option",
  testFunction: (value: any) => {
    if (value) {
      return true;
    }
  },
};
export const wellbeingFormValidation = Yup.object({
  BreathingToday: Yup.number().test(
    testForDropdownNoOptionSelected.label,
    testForDropdownNoOptionSelected.errorMessage,
    testForDropdownNoOptionSelected.testFunction
  ),
  BreathingAffectingDailyActivities: Yup.number().test(
    testForDropdownNoOptionSelected.label,
    testForDropdownNoOptionSelected.errorMessage,
    testForDropdownNoOptionSelected.testFunction
  ),
  PhysicalActivity: Yup.number().test(
    testForDropdownNoOptionSelected.label,
    testForDropdownNoOptionSelected.errorMessage,
    testForDropdownNoOptionSelected.testFunction
  ),
  // Check validation only if HaveCoughToday is true
  HowIsCough: Yup.number().when("HaveCoughToday", {
    is: true,
    then: (schema) =>
      schema.test(
        testForDropdownNoOptionSelected.label,
        testForDropdownNoOptionSelected.errorMessage,
        testForDropdownNoOptionSelected.testFunction
      ),
  }),

  SputumProducingToday: Yup.number().test(
    testForDropdownNoOptionSelected.label,
    testForDropdownNoOptionSelected.errorMessage,
    testForDropdownNoOptionSelected.testFunction
  ),
  ColorOfSputum: Yup.number().when("SputumProducingToday", {
    is: (value: number) => [2, 3, 4, 5].includes(value),
    then: (schema) =>
      schema.test(
        testForDropdownNoOptionSelected.label,
        testForDropdownNoOptionSelected.errorMessage,
        testForDropdownNoOptionSelected.testFunction
      ),
  }),
  // Check validation only if FeelTiredToday is true
  IsYourTiredness: Yup.number().when("FeelTiredToday", {
    is: true,
    then: (schema) =>
      schema.test(
        testForDropdownNoOptionSelected.label,
        testForDropdownNoOptionSelected.errorMessage,
        testForDropdownNoOptionSelected.testFunction
      ),
  }),
  // Check validation only if AnkleSwelling is true
  IsAnkleSwelling: Yup.number().when("AnkleSwelling", {
    is: true,
    then: (schema) =>
      schema.test(
        testForDropdownNoOptionSelected.label,
        testForDropdownNoOptionSelected.errorMessage,
        testForDropdownNoOptionSelected.testFunction
      ),
  }),
  // Check validation only if FlareUpToday is true
  HaveYou: Yup.number().when("FlareUpToday", {
    is: true,
    then: (schema) =>
      schema.test(
        testForDropdownNoOptionSelected.label,
        testForDropdownNoOptionSelected.errorMessage,
        testForDropdownNoOptionSelected.testFunction
      ),
  }),
  TreatmentAntibiotics: Yup.number().when(
    ["HaveYou", "RecievingTreatmentFlareUp", "HospitalisedForExacerbation"],
    {
      is: (
        HaveYou: number,
        RecievingTreatmentFlareUp: boolean,
        HospitalisedForExacerbation: boolean
      ) => {
        if (HaveYou === 1) {
          return true;
        } else if (HaveYou === 2 || HaveYou === 3) {
          if (RecievingTreatmentFlareUp) {
            return true;
          }
        } else if (HaveYou === 4) {
          if (RecievingTreatmentFlareUp || HospitalisedForExacerbation) {
            return true;
          }
        }
      },
      then: (schema) =>
        schema.test(
          testForDropdownNoOptionSelected.label,
          testForDropdownNoOptionSelected.errorMessage,
          testForDropdownNoOptionSelected.testFunction
        ),
    }
  ),
});

export const biomarkersFormValidation = Yup.object({
  CRP: Yup.number().required("Please enter CRP value"),
  // PCT: Yup.number().required("Please enter PCT value"),
});
