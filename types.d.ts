export interface ClinicHistoryItem {
  Id: number;
  CreatedDate: string;
  CreatedByName: string;
  Age: number;
  BMI: number;
  CONotes: string;
  Calc: number;
  ClientId: number;
  ClinicalObservationNotes: string;
  Condition: string;
  CopdHistory: number;
  CreatedBy: number;
  CreatedByName: string;
  CreatedDate: string;
  EthiniCity: string;
  FlareupHistory1: number;
  FlareupHistory2: number;
  FlareupHistory3: number;
  FlareupHistory4: number;
  FlareupHistory5: number;
  Gender: number;
  Height: number;
  Hospitalise: number;
  IsArchived: boolean;
  IsFirstRecord: boolean;
  MRCScale: number;
  NhsId: string;
  OxyAmbulatoryFlowRate: number;
  OxyLTOTFlowRate: number;
  OxyShortBurstFlowRate: number;
  Oxyzen: string;
  PId: number;
  PackYears: number;
  PatientName: string;
  PulmRehab: boolean;
  PulmRehabStartDate: string;
  RespMedications: string;
  ShortofBreath: boolean;
  SmokingHabit: number;
  Symptom: string;
  UpdatedBy: number;
  UpdatedDate: string;
  Weight: number;
}

export interface WellBeingAssessment {
  AnkleSwelling: boolean;
  AnyOtherAntibiotics: string;
  BreathingAffectingDailyActivities: number;
  BreathingToday: number;
  ChestPainTightness: boolean;
  ColorOfSputum: number;
  FeelTiredToday: boolean;
  FlareUpToday: boolean;
  HaveCoughToday: boolean;
  HaveYou: number;
  Heartburn: boolean;
  HeartburnWokenAtNight: boolean;
  HospitalisedForExacerbation: boolean;
  HowIsCough: number;
  IsAnkleSwelling: number;
  IsYourTiredness: number;
  OtherConcern: string;
  PhysicalActivity: number;
  RecievingTreatmentFlareUp: boolean;
  SputumProducingToday: number;
  TreatmentAntibiotics: number;
  TreatmentOralSteroid: string;
  TreatmentOther: string;
}

export interface BiometricsAssessment {
  CRP: string;
  PCT: string;
}
