import { ClinicHistoryItem } from "@/types";
import { create } from "zustand";

interface ClinicHistoryState {
  clinicHistory: ClinicHistoryItem[];
  setClinicHistory: (clinicHistory: ClinicHistoryItem[]) => void;
  getClinicHistoryById: (id: number) => ClinicHistoryItem | undefined;
}

export const useClinicHistoryStore = create<ClinicHistoryState>()(
  (set, get) => ({
    clinicHistory: [],
    setClinicHistory: (clinicHistory) => set({ clinicHistory }),
    getClinicHistoryById: (id: number) => {
      return get().clinicHistory.find(
        (clinicHistory: ClinicHistoryItem) => clinicHistory.Id === id
      );
    },
  })
);
