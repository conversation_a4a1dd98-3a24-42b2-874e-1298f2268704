import { create } from "zustand";

export type Appointment = {
  Id: number;
  AppointmentDate: string;
  AppointmentTime: string;
  CheckUpType: string;
  ClinicianName: string;
  AppointmentStatus: string;
};

interface AppointmentState {
  appointments: Appointment[];
  setAppointments: (appointments: Appointment[]) => void;
  getAppointmentById: (id: number) => Appointment | undefined;
}

export const useAppointmentStore = create<AppointmentState>()((set, get) => ({
  appointments: [],
  setAppointments: (appointments) => set({ appointments }),
  getAppointmentById: (id) => {
    return get().appointments.find(
      (appointment: Appointment) => appointment.Id === id
    );
  },
}));
