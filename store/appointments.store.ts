import { create } from "zustand";

export type Appointment = {
  Id: number;
  AppointmentDate: string;
  AppointmentTime: string;
  CheckUpType: string;
  ClinicianName: string;
  AppointmentStatus: string;
};

interface AppointmentState {
  unconfirmedAppointments: Appointment[];
  confirmedAppointments: Appointment[];
  cancelledAppointments: Appointment[];
  setAppointments: (appointments: Appointment[], status: string) => void;
  getAppointmentById: (id: number, status: string) => Appointment | undefined;
  getAppointmentsByStatus: (status: string) => Appointment[];
}

export const useAppointmentStore = create<AppointmentState>()((set, get) => ({
  unconfirmedAppointments: [],
  confirmedAppointments: [],
  cancelledAppointments: [],
  setAppointments: (appointments, status) => {
    if (status === "Pending") {
      set({ unconfirmedAppointments: appointments });
    } else if (status === "Scheduled") {
      set({ confirmedAppointments: appointments });
    } else if (status === "Cancelled") {
      set({ cancelledAppointments: appointments });
    }
  },
  getAppointmentsByStatus: (status) => {
    if (status === "Pending") {
      return get().unconfirmedAppointments;
    } else if (status === "Scheduled") {
      return get().confirmedAppointments;
    } else if (status === "Cancelled") {
      return get().cancelledAppointments;
    }
    return [];
  },
  getAppointmentById: (id, status) => {
    if (status === "Pending") {
      return get().unconfirmedAppointments.find(
        (appointment: Appointment) => appointment.Id === id
      );
    } else if (status === "Scheduled") {
      return get().confirmedAppointments.find(
        (appointment: Appointment) => appointment.Id === id
      );
    } else if (status === "Cancelled") {
      return get().cancelledAppointments.find(
        (appointment: Appointment) => appointment.Id === id
      );
    }
  },
}));
