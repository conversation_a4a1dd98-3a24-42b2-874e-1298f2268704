import { STAYING_HEALTHY_CARDS } from "@/data";
import { create } from "zustand";

interface StayingHealthyState {
  stayingHealthyCards: any[];
  setStayingHealthyCards: (stayingHealthyCards: any[]) => void;
  getStayingHealthyCardById: (id: number) => any | undefined;
}

export const useStayingHealthyStore = create<StayingHealthyState>()(
  (set, get) => ({
    stayingHealthyCards: STAYING_HEALTHY_CARDS,
    setStayingHealthyCards: (stayingHealthyCards) =>
      set({ stayingHealthyCards }),
    getStayingHealthyCardById: (id: number) => {
      return get().stayingHealthyCards.find(
        (stayingHealthyCard: any) => stayingHealthyCard.id === id
      );
    },
  })
);
