const { Dimensions } = require("react-native");
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get("window");
const SCREEN_PADDING = 10;

const SHADOW = "0 2px 4px rgba(0, 0, 0, 0.1)";

//COLORS
const COLORS = {
  black: "#0A0A0A",
  white: "#fff",
  blue: "#306CFE",
  gray: "#797B7C",
  cardBg: "#0D66D00A",
  borderGray: "#C7C4C4",
  rippleColor: "rgba(0, 0, 0, .32)",
};

export { COLORS, SCREEN_HEIGHT, SCREEN_PADDING, SCREEN_WIDTH, SHADOW };
