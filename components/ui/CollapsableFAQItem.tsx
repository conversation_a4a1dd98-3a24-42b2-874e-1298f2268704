import React from "react";
import { Platform, StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";
import { WebView } from "react-native-webview";

type CollapsableFAQItemProps = {
  question: string;
  answer: string;
};

const CollapsableFAQItem = ({ question, answer }: CollapsableFAQItemProps) => {
  const [webviewHeght, setWebviewHeight] = React.useState(1);
  //   console.log("answer", answer);

  const _onWebViewMessage = (event) => {
    console.log("_onWebViewMessage got called");

    setWebviewHeight(Number(event.nativeEvent.data));
  };
  console.log(webviewHeght, "webviewHeght");

  return (
    <View style={styles.container}>
      {
        //answer is html string
      }
      <Text variant="titleLarge">{question}</Text>
      <WebView
        style={{ height: webviewHeght, backgroundColor: "transparent" }}
        originWhitelist={["*"]}
        source={{
          html: `<!DOCTYPE html><head>" 
            <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1 "></head> <body>
            answer +
            '</body>  <script>
    ${
      Platform.OS === "ios"
        ? "window.onload = () => window.ReactNativeWebView.postMessage(document.body.scrollHeight)"
        : "window.onresize = () => window.ReactNativeWebView.postMessage(document.body.scrollHeight)"
    }
  </script></html>`,
        }}
        onMessage={_onWebViewMessage}
        scrollEnabled={false}
        cacheEnabled={false}
        pullToRefreshEnabled={false}
      />
    </View>
  );
};

export default CollapsableFAQItem;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    padding: 20,
  },
  webview: {
    width: "100%",
  },
});
