import React, { useState } from "react";
import { StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";
import { WebView } from "react-native-webview";

type CollapsableFAQItemProps = {
  question: string;
  answer: string;
};

const CollapsableFAQItem = ({ question, answer }: CollapsableFAQItemProps) => {
  const [webViewHeight, setWebViewHeight] = useState(100); // Default height
  console.log(question, answer);

  // JavaScript to inject into WebView to measure content height
  const injectedJavaScript = `
    (function() {
      function sendHeight() {
        // Wait a bit for layout to settle
        setTimeout(() => {
          // Force layout recalculation
          document.body.offsetHeight;

          // Method 1: Calculate height by measuring all child elements
          function calculateContentHeight() {
            let totalHeight = 0;
            const children = document.body.children;

            for (let i = 0; i < children.length; i++) {
              const child = children[i];
              const rect = child.getBoundingClientRect();
              const computedStyle = window.getComputedStyle(child);

              // Include margins in the calculation
              const marginTop = parseFloat(computedStyle.marginTop) || 0;
              const marginBottom = parseFloat(computedStyle.marginBottom) || 0;

              totalHeight += rect.height + marginTop + marginBottom;
            }

            // No body padding needed since we set it to 0
            return totalHeight;
          }

          // Method 2: Traditional measurements
          const bodyRect = document.body.getBoundingClientRect();
          const bodyHeight = bodyRect.height;
          const scrollHeight = document.body.scrollHeight;
          const offsetHeight = document.body.offsetHeight;

          // Method 3: Calculate based on content
          const calculatedHeight = calculateContentHeight();

          // Use the most accurate measurement
          const measurements = [bodyHeight, scrollHeight, offsetHeight, calculatedHeight].filter(h => h > 0);
          const finalHeight = Math.max(...measurements, 50);

          console.log('Height measurements:', {
            bodyHeight,
            scrollHeight,
            offsetHeight,
            calculatedHeight,
            finalHeight
          });

          window.ReactNativeWebView.postMessage(JSON.stringify({ height: finalHeight }));
        }, 200);
      }

      // Send height when content is loaded
      if (document.readyState === 'complete') {
        sendHeight();
      } else {
        window.addEventListener('load', sendHeight);
      }

      // Also send height when DOM content is loaded
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', sendHeight);
      }

      // Send height when images are loaded (in case content contains images)
      const images = document.getElementsByTagName('img');
      for (let i = 0; i < images.length; i++) {
        images[i].addEventListener('load', sendHeight);
      }

      // Multiple attempts to get accurate height
      setTimeout(sendHeight, 200);
   ;
    })();
    true; // Required for injectedJavaScript
  `;

  // Handle messages from WebView
  const handleMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.height) {
        // Add some padding to prevent content cutoff
        setWebViewHeight(data.height);
      }
    } catch (error) {
      console.warn("Error parsing WebView message:", error);
    }
  };

  // Enhanced HTML with better styling for consistent rendering
  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <style>
          html, body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 16px;
            color: #333;
            background-color: transparent;
            height: auto;
            min-height: 0;
          }
          * {
            max-width: 100%;
            box-sizing: border-box;
          }
          img {
            height: auto;
            display: block;
            max-width: 100%;
          }
          p {
            margin: 0 0 0 0;
          }
          p:last-child {
            margin-bottom: 0;
          }
          /* Remove any default margins that might cause extra space */
          h1, h2, h3, h4, h5, h6 {
            margin: 0 0 0 0;
          }
          h1:last-child, h2:last-child, h3:last-child,
          h4:last-child, h5:last-child, h6:last-child {
            margin-bottom: 0;
          }
          ul, ol {
            margin: 0;
            padding: 0;
            margin-left: 0;
            padding-left: 0;
          }
          li {
            margin: 0 0 0 0;
            padding: 0;
            padding-left: 0;
            margin-left: 0;
            line-height: 1.4;
          }
          li:last-child {
            margin-bottom: 0;
          }
          /* Ensure nested lists work properly */
          li ul, li ol {
         
          }
          div:last-child {
            margin-bottom: 0;
          }
          blockquote {
            margin: 0 0 0 0;
            padding: 10px 15px;
            border-left: 4px solid #ddd;
            background-color: #f9f9f9;
            font-style: italic;
          }
          blockquote:last-child {
            margin-bottom: 0;
          }
        </style>
      </head>
      <body>
        ${answer}
      </body>
    </html>
  `;

  return (
    <View style={styles.container}>
      <Text variant="titleMedium">{question}</Text>
      <WebView
        originWhitelist={["*"]}
        source={{
          html: htmlContent,
        }}
        style={[styles.webview, { height: webViewHeight }]}
        scrollEnabled={false}
        cacheEnabled={false}
        pullToRefreshEnabled={false}
        injectedJavaScript={injectedJavaScript}
        onMessage={handleMessage}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={false}
        scalesPageToFit={false}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

export default CollapsableFAQItem;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    padding: 20,
  },
  webview: {
    width: "100%",
  },
});
