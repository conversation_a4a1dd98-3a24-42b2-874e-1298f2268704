import React, { useState } from "react";
import { StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";
import { WebView } from "react-native-webview";

type CollapsableFAQItemProps = {
  question: string;
  answer: string;
};

const CollapsableFAQItem = ({ question, answer }: CollapsableFAQItemProps) => {
  const [webViewHeight, setWebViewHeight] = useState(100); // Default height

  // JavaScript to inject into WebView to measure content height
  const injectedJavaScript = `
    (function() {
      function sendHeight() {
        const height = Math.max(
          document.body.scrollHeight,
          document.body.offsetHeight,
          document.documentElement.clientHeight,
          document.documentElement.scrollHeight,
          document.documentElement.offsetHeight
        );
        window.ReactNativeWebView.postMessage(JSON.stringify({ height: height }));
      }

      // Send height when content is loaded
      if (document.readyState === 'complete') {
        sendHeight();
      } else {
        window.addEventListener('load', sendHeight);
      }

      // Also send height when DOM content is loaded
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', sendHeight);
      }

      // Send height when images are loaded (in case content contains images)
      const images = document.getElementsByTagName('img');
      for (let i = 0; i < images.length; i++) {
        images[i].addEventListener('load', sendHeight);
      }
    })();
    true; // Required for injectedJavaScript
  `;

  // Handle messages from WebView
  const handleMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.height) {
        // Add some padding to prevent content cutoff
        setWebViewHeight(data.height);
      }
    } catch (error) {
      console.warn("Error parsing WebView message:", error);
    }
  };

  // Enhanced HTML with better styling for consistent rendering
  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <style>
          body {
            margin: 0;
            padding: 10px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 16px;
            line-height: 1.5;
            color: #333;
            background-color: transparent;
          }
          * {
            max-width: 100%;
            box-sizing: border-box;
          }
          img {
            height: auto;
            display: block;
          }
          p {
            margin: 0 0 10px 0;
          }
          p:last-child {
            margin-bottom: 0;
          }
        </style>
      </head>
      <body>
        ${answer}
      </body>
    </html>
  `;

  return (
    <View style={styles.container}>
      <Text variant="titleLarge">{question}</Text>
      <WebView
        originWhitelist={["*"]}
        source={{
          html: htmlContent,
        }}
        style={[styles.webview, { height: webViewHeight }]}
        scrollEnabled={false}
        cacheEnabled={false}
        pullToRefreshEnabled={false}
        injectedJavaScript={injectedJavaScript}
        onMessage={handleMessage}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={false}
        scalesPageToFit={false}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

export default CollapsableFAQItem;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    padding: 20,
  },
  webview: {
    width: "100%",
  },
});
