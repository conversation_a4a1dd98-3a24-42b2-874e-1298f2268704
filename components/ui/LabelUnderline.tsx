import { COLORS } from "@/constants";
import React from "react";
import { StyleSheet, Text } from "react-native";

const LabelUnderline = ({ label, style }: { label: string; style?: any }) => {
  return <Text style={[styles.label, style]}>{label}</Text>;
};

export default LabelUnderline;

const styles = StyleSheet.create({
  label: {
    color: COLORS.black,
    fontFamily: "Nunito-Medium",
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray,
    marginVertical: 16,
    fontSize: 18,
    padding: 8,
  },
});
