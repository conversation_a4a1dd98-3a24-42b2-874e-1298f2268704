import { SCREEN_PADDING } from "@/constants";
import { DrawerActions, useNavigation } from "@react-navigation/native";
import React from "react";
import { StyleSheet, View } from "react-native";
import { Icon, TouchableRipple } from "react-native-paper";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const Header = ({ title = "Header", showBackButton = false, ...props }) => {
  const isRoot = props?.route?.name === "index";

  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.container, { paddingTop: insets.top + 4 }]}>
      <TouchableRipple
        rippleColor="transparent"
        onPress={() => {
          isRoot
            ? navigation.dispatch(DrawerActions.toggleDrawer())
            : navigation.goBack();
        }}
      >
        <Icon
          source={isRoot ? "menu" : "arrow-left"}
          size={28}
          color="#306CFE"
        />
      </TouchableRipple>
      <Icon source="bell-outline" size={28} color="#0A0A0A" />
    </View>
  );
};

export default Header;

const styles = StyleSheet.create({
  container: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
    paddingHorizontal: SCREEN_PADDING * 2,
    paddingVertical: 10,
    justifyContent: "space-between",
    backgroundColor: "#FFF",
  },
});
