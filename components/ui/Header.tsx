import { COLORS, SCREEN_PADDING } from "@/constants";
import Feather from "@expo/vector-icons/Feather";
import { DrawerActions, useNavigation } from "@react-navigation/native";
import { Link } from "expo-router";
import React from "react";
import { Pressable, StyleSheet, Text, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const Header = ({ title = "Header", showBackButton = false, ...props }) => {
  const screenTitle = props.options.title;
  const screen = props?.route?.name;
  const isRoot = screen === "index";

  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.container, { paddingTop: insets.top + 4 }]}>
      {/* left button */}
      <Pressable
        style={{ flexDirection: "row", alignItems: "center", gap: 8 }}
        onPress={() => {
          isRoot
            ? navigation.dispatch(DrawerActions.toggleDrawer())
            : navigation.goBack();
        }}
      >
        <Feather
          name={isRoot ? "menu" : "chevron-left"}
          size={24}
          color={isRoot ? "#306CFE" : "#0A0A0A"}
        />
        {!isRoot && (
          <Text
            style={{
              fontFamily: "Nunito-Medium",
              fontSize: 15.8,
              color: COLORS.black,
            }}
          >
            Back
          </Text>
        )}
      </Pressable>

      {/* right button */}
      <Link
        href={
          screenTitle === "My Messages"
            ? "/(drawer)/(my-messages)/compose"
            : "/profile"
        }
        asChild
      >
        <Pressable>
          <Feather
            name={screenTitle === "My Messages" ? "plus" : "bell"}
            size={24}
            color={COLORS.black}
          />
        </Pressable>
      </Link>
    </View>
  );
};

export default Header;

const styles = StyleSheet.create({
  container: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
    paddingHorizontal: SCREEN_PADDING * 2,
    paddingVertical: 10,
    justifyContent: "space-between",
    backgroundColor: "#FFF",
  },
});
