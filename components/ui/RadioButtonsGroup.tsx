import React, { memo, useCallback } from "react";
import { Pressable, StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";

const RadioButtonsGroup = ({ value, setFieldValue, field }: any) => {
  const setOptionYesValue = useCallback(() => setFieldValue(field, true), []);
  const setOptionNoValue = useCallback(() => setFieldValue(field, false), []);

  return (
    <View style={styles.container}>
      <Pressable style={styles.buttonPress} onPress={setOptionYesValue}>
        <View style={styles.radioButton}>
          {value && <View style={styles.selectedButton} />}
        </View>
        <Text variant="labelLarge">Yes</Text>
      </Pressable>
      <Pressable style={styles.buttonPress} onPress={setOptionNoValue}>
        <View style={styles.radioButton}>
          {!value && <View style={styles.selectedButton} />}
        </View>
        <Text variant="labelLarge">No</Text>
      </Pressable>
    </View>
  );
};

export default memo(RadioButtonsGroup);

const styles = StyleSheet.create({
  container: { flexDirection: "row", gap: 40 },
  buttonPress: {
    // backgroundColor: "green",
    flexDirection: "row",
    gap: 10,
    padding: 10,
  },
  radioButton: {
    // backgroundColor: "red",
    width: 22,
    height: 22,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#979797",
  },
  selectedButton: {
    backgroundColor: "#0D66D0",
    width: 12,
    height: 12,
    borderRadius: 12,
  },
});
