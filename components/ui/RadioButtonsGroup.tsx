import React, { memo } from "react";
import { Pressable, StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";

type RadioButtonsGroupProps = {
  value: boolean;
  setValue: (selection: boolean) => void;
  style: {};
};

const RadioButtonsGroup = ({
  value,
  setValue,
  style,
}: RadioButtonsGroupProps) => {
  return (
    <View style={[styles.container, style]}>
      <Pressable style={styles.buttonPress} onPress={() => setValue(true)}>
        <View style={styles.radioButton}>
          {value && <View style={styles.selectedButton} />}
        </View>
        <Text variant="labelLarge">Yes</Text>
      </Pressable>
      <Pressable style={styles.buttonPress} onPress={() => setValue(false)}>
        <View style={styles.radioButton}>
          {!value && <View style={styles.selectedButton} />}
        </View>
        <Text variant="labelLarge">No</Text>
      </Pressable>
    </View>
  );
};

export default memo(RadioButtonsGroup);

const styles = StyleSheet.create({
  container: { flexDirection: "row", gap: 40 },
  buttonPress: {
    // backgroundColor: "green",
    flexDirection: "row",
    gap: 10,
    padding: 10,
  },
  radioButton: {
    // backgroundColor: "red",
    width: 22,
    height: 22,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#979797",
  },
  selectedButton: {
    backgroundColor: "#0D66D0",
    width: 12,
    height: 12,
    borderRadius: 12,
  },
});
