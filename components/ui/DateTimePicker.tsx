import React, { useState } from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import { Text } from "react-native-paper";

interface DateTimePickerProps {
  onDateTimeChange?: (date: Date) => void;
  initialDate?: Date;
}

const DateTimePicker = ({
  onDateTimeChange,
  initialDate = new Date(),
}: DateTimePickerProps) => {
  const [selectedDate, setSelectedDate] = useState(initialDate);
  const [currentMonth, setCurrentMonth] = useState(initialDate.getMonth());
  const [currentYear, setCurrentYear] = useState(initialDate.getFullYear());
  const [hours, setHours] = useState(initialDate.getHours() % 12 || 12);
  const [minutes, setMinutes] = useState(initialDate.getMinutes());
  const [isAM, setIsAM] = useState(initialDate.getHours() < 12);

  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const weekDays = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"];

  const getDaysInMonth = (month: number, year: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (month: number, year: number) => {
    return new Date(year, month, 1).getDay();
  };

  const generateCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentMonth, currentYear);
    const firstDay = getFirstDayOfMonth(currentMonth, currentYear);
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }

    return days;
  };

  const handleDateSelect = (day: number) => {
    const newDate = new Date(
      currentYear,
      currentMonth,
      day,
      isAM ? hours % 12 : (hours % 12) + 12,
      minutes
    );
    setSelectedDate(newDate);
    onDateTimeChange?.(newDate);
  };

  const handleTimeChange = () => {
    const newDate = new Date(selectedDate);
    newDate.setHours(isAM ? hours % 12 : (hours % 12) + 12);
    newDate.setMinutes(minutes);
    setSelectedDate(newDate);
    onDateTimeChange?.(newDate);
  };

  const navigateMonth = (direction: "prev" | "next") => {
    if (direction === "prev") {
      if (currentMonth === 0) {
        setCurrentMonth(11);
        setCurrentYear(currentYear - 1);
      } else {
        setCurrentMonth(currentMonth - 1);
      }
    } else {
      if (currentMonth === 11) {
        setCurrentMonth(0);
        setCurrentYear(currentYear + 1);
      } else {
        setCurrentMonth(currentMonth + 1);
      }
    }
  };

  const formatTime = () => {
    const formattedHours = hours.toString().padStart(2, "0");
    const formattedMinutes = minutes.toString().padStart(2, "0");
    return `${formattedHours}:${formattedMinutes}`;
  };

  const calendarDays = generateCalendarDays();

  return (
    <View style={styles.container}>
      <Text variant="headlineSmall" style={styles.title}>
        Select Date & Time:
      </Text>

      {/* Month Navigation */}
      <View style={styles.monthHeader}>
        <TouchableOpacity onPress={() => navigateMonth("prev")}>
          <Text style={styles.navButton}>‹</Text>
        </TouchableOpacity>

        <Text style={styles.monthYear}>
          {months[currentMonth]} {currentYear}
        </Text>

        <TouchableOpacity onPress={() => navigateMonth("next")}>
          <Text style={styles.navButton}>›</Text>
        </TouchableOpacity>
      </View>

      {/* Week Days Header */}
      <View style={styles.weekDaysContainer}>
        {weekDays.map((day) => (
          <Text key={day} style={styles.weekDay}>
            {day}
          </Text>
        ))}
      </View>

      {/* Calendar Grid */}
      <View style={styles.calendarGrid}>
        {calendarDays.map((day, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.dayCell,
              day === selectedDate.getDate() &&
                currentMonth === selectedDate.getMonth() &&
                currentYear === selectedDate.getFullYear() &&
                styles.selectedDay,
            ]}
            onPress={() => day && handleDateSelect(day)}
            disabled={!day}
          >
            <Text
              style={[
                styles.dayText,
                day === selectedDate.getDate() &&
                  currentMonth === selectedDate.getMonth() &&
                  currentYear === selectedDate.getFullYear() &&
                  styles.selectedDayText,
              ]}
            >
              {day || ""}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Time Picker */}
      <View style={styles.timeContainer}>
        <Text variant="headlineSmall" style={styles.timeLabel}>
          Time
        </Text>

        <View style={styles.timePickerContainer}>
          <Text style={styles.timeDisplay}>{formatTime()}</Text>

          <View style={styles.ampmContainer}>
            <TouchableOpacity
              style={[styles.ampmButton, isAM && styles.selectedAmPm]}
              onPress={() => {
                setIsAM(true);
                handleTimeChange();
              }}
            >
              <Text style={[styles.ampmText, isAM && styles.selectedAmPmText]}>
                AM
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.ampmButton, !isAM && styles.selectedAmPm]}
              onPress={() => {
                setIsAM(false);
                handleTimeChange();
              }}
            >
              <Text style={[styles.ampmText, !isAM && styles.selectedAmPmText]}>
                PM
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Time Controls */}
        <View style={styles.timeControls}>
          <View style={styles.timeControlGroup}>
            <Text style={styles.timeControlLabel}>Hours</Text>
            <View style={styles.timeControlButtons}>
              <TouchableOpacity
                style={styles.timeControlButton}
                onPress={() => {
                  const newHours = hours === 1 ? 12 : hours - 1;
                  setHours(newHours);
                  handleTimeChange();
                }}
              >
                <Text style={styles.timeControlButtonText}>-</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.timeControlButton}
                onPress={() => {
                  const newHours = hours === 12 ? 1 : hours + 1;
                  setHours(newHours);
                  handleTimeChange();
                }}
              >
                <Text style={styles.timeControlButtonText}>+</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.timeControlGroup}>
            <Text style={styles.timeControlLabel}>Minutes</Text>
            <View style={styles.timeControlButtons}>
              <TouchableOpacity
                style={styles.timeControlButton}
                onPress={() => {
                  const newMinutes = minutes === 0 ? 59 : minutes - 1;
                  setMinutes(newMinutes);
                  handleTimeChange();
                }}
              >
                <Text style={styles.timeControlButtonText}>-</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.timeControlButton}
                onPress={() => {
                  const newMinutes = minutes === 59 ? 0 : minutes + 1;
                  setMinutes(newMinutes);
                  handleTimeChange();
                }}
              >
                <Text style={styles.timeControlButtonText}>+</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    padding: 20,
    borderRadius: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000",
    marginBottom: 20,
  },
  monthHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  monthYear: {
    fontSize: 20,
    fontWeight: "600",
    color: "#2196F3",
  },
  navButton: {
    fontSize: 24,
    color: "#2196F3",
    paddingHorizontal: 15,
    paddingVertical: 5,
  },
  weekDaysContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 10,
  },
  weekDay: {
    fontSize: 14,
    color: "#999",
    fontWeight: "500",
    width: 40,
    textAlign: "center",
  },
  calendarGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-around",
    marginBottom: 30,
  },
  dayCell: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 5,
  },
  selectedDay: {
    backgroundColor: "#2196F3",
    borderRadius: 20,
  },
  dayText: {
    fontSize: 16,
    color: "#2196F3",
    fontWeight: "500",
  },
  selectedDayText: {
    color: "#fff",
    fontWeight: "bold",
  },
  timeContainer: {
    marginTop: 20,
  },
  timeLabel: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000",
    marginBottom: 15,
  },
  timePickerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  timeDisplay: {
    fontSize: 32,
    fontWeight: "300",
    color: "#000",
  },
  ampmContainer: {
    flexDirection: "row",
  },
  ampmButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginHorizontal: 5,
    borderRadius: 8,
    backgroundColor: "#f0f0f0",
  },
  selectedAmPm: {
    backgroundColor: "#2196F3",
  },
  ampmText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#666",
  },
  selectedAmPmText: {
    color: "#fff",
  },
  timeControls: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  timeControlGroup: {
    alignItems: "center",
  },
  timeControlLabel: {
    fontSize: 14,
    color: "#666",
    marginBottom: 10,
  },
  timeControlButtons: {
    flexDirection: "row",
    gap: 10,
  },
  timeControlButton: {
    width: 40,
    height: 40,
    backgroundColor: "#f0f0f0",
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  timeControlButtonText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#2196F3",
  },
});

export default DateTimePicker;
