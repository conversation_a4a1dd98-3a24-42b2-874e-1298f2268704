import { COLORS } from "@/constants";
import React, { useState } from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import { Text } from "react-native-paper";

interface DateTimePickerProps {
  onDateTimeChange?: (date: Date) => void;
  initialDate?: Date;
}

const DateTimePicker = ({
  onDateTimeChange,
  initialDate = new Date(),
}: DateTimePickerProps) => {
  const [selectedDate, setSelectedDate] = useState(initialDate);
  const [currentMonth, setCurrentMonth] = useState(initialDate.getMonth());
  const [currentYear, setCurrentYear] = useState(initialDate.getFullYear());
  const [hours, setHours] = useState(initialDate.getHours() % 12 || 12);
  const [minutes, setMinutes] = useState(initialDate.getMinutes());
  const [isAM, setIsAM] = useState(initialDate.getHours() < 12);

  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const weekDays = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"];

  const getDaysInMonth = (month: number, year: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (month: number, year: number) => {
    return new Date(year, month, 1).getDay();
  };

  const generateCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentMonth, currentYear);
    const firstDay = getFirstDayOfMonth(currentMonth, currentYear);
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }

    return days;
  };

  const handleDateSelect = (day: number) => {
    const newDate = new Date(
      currentYear,
      currentMonth,
      day,
      isAM ? hours % 12 : (hours % 12) + 12,
      minutes
    );
    setSelectedDate(newDate);
    onDateTimeChange?.(newDate);
  };

  const handleTimeChange = () => {
    const newDate = new Date(selectedDate);
    newDate.setHours(isAM ? hours % 12 : (hours % 12) + 12);
    newDate.setMinutes(minutes);
    setSelectedDate(newDate);
    onDateTimeChange?.(newDate);
  };

  const navigateMonth = (direction: "prev" | "next") => {
    if (direction === "prev") {
      if (currentMonth === 0) {
        setCurrentMonth(11);
        setCurrentYear(currentYear - 1);
      } else {
        setCurrentMonth(currentMonth - 1);
      }
    } else {
      if (currentMonth === 11) {
        setCurrentMonth(0);
        setCurrentYear(currentYear + 1);
      } else {
        setCurrentMonth(currentMonth + 1);
      }
    }
  };

  const formatTime = () => {
    const formattedHours = hours.toString().padStart(2, "0");
    const formattedMinutes = minutes.toString().padStart(2, "0");
    return `${formattedHours}:${formattedMinutes}`;
  };

  const calendarDays = generateCalendarDays();

  return (
    <View style={styles.container}>
      {/* <Text variant="headlineSmall" style={styles.title}>
        Select Date & Time:
      </Text> */}

      {/* Month Navigation */}
      <View style={styles.monthHeader}>
        <TouchableOpacity onPress={() => navigateMonth("prev")}>
          <Text style={styles.navButton}>‹</Text>
        </TouchableOpacity>

        <Text style={styles.monthYear}>
          {months[currentMonth]} {currentYear}
        </Text>

        <TouchableOpacity onPress={() => navigateMonth("next")}>
          <Text style={styles.navButton}>›</Text>
        </TouchableOpacity>
      </View>

      {/* Week Days Header */}
      <View style={styles.weekDaysContainer}>
        {weekDays.map((day) => (
          <Text key={day} style={styles.weekDay}>
            {day}
          </Text>
        ))}
      </View>

      {/* Calendar Grid */}
      <View style={styles.calendarGrid}>
        {calendarDays.map((day, index) => {
          const isSelected =
            day === selectedDate.getDate() &&
            currentMonth === selectedDate.getMonth() &&
            currentYear === selectedDate.getFullYear();

          return (
            <TouchableOpacity
              key={index}
              style={styles.dayCell}
              onPress={() => day && handleDateSelect(day)}
              disabled={!day}
            >
              <View style={[styles.dayInner, isSelected && styles.selectedDay]}>
                <Text
                  style={[styles.dayText, isSelected && styles.selectedDayText]}
                >
                  {day || ""}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Time Picker */}
      <View style={styles.timeContainer}>
        <View style={styles.timeRow}>
          <Text variant="headlineSmall" style={styles.timeLabel}>
            Time
          </Text>
          <View style={{ flexDirection: "row", alignItems: "center", gap: 16 }}>
            <View style={styles.timeDisplay}>
              <Text
                style={{
                  fontSize: 22,
                  color: "#000",
                  letterSpacing: 0.35,
                  lineHeight: 28,
                }}
              >
                {formatTime()}
              </Text>
            </View>

            <View style={styles.ampmContainer}>
              <TouchableOpacity
                style={[styles.ampmButton, isAM && styles.selectedAmPm]}
                onPress={() => {
                  setIsAM(true);
                  handleTimeChange();
                }}
              >
                <Text
                  style={[styles.ampmText, isAM && styles.selectedAmPmText]}
                >
                  AM
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.ampmButton, !isAM && styles.selectedAmPm]}
                onPress={() => {
                  setIsAM(false);
                  handleTimeChange();
                }}
              >
                <Text
                  style={[styles.ampmText, !isAM && styles.selectedAmPmText]}
                >
                  PM
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    borderRadius: 13,
    padding: 16,
    shadowColor: "#000",
    boxShadow: "0 10px 60px 0px rgba(0, 0, 0, 0.1)",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000",
    marginBottom: 4,
  },
  monthHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  monthYear: {
    fontSize: 20,
    lineHeight: 24,
    letterSpacing: 0.38,
    fontWeight: "bold",
    color: "#0D66D0",
  },
  navButton: {
    fontSize: 24,
    color: "#0D66D0",
    paddingHorizontal: 15,
    paddingVertical: 5,
  },
  weekDaysContainer: {
    flexDirection: "row",
    justifyContent: "flex-start",
    marginBottom: 10,
  },
  weekDay: {
    fontSize: 14,
    color: "#3C3C434D",
    fontWeight: "500",
    width: "14.28%", // 100% / 7 days = 14.28%
    textAlign: "center",
  },
  calendarGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "flex-start",
    marginBottom: 0,
  },
  dayCell: {
    width: "14.28%", // 100% / 7 days = 14.28%
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 5,
  },
  dayInner: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  selectedDay: {
    backgroundColor: "#0D66D0",
    borderRadius: 20,
  },
  dayText: {
    fontSize: 16,
    color: "#0D66D0",
    fontWeight: "500",
  },
  selectedDayText: {
    color: "#fff",
    fontWeight: "bold",
  },
  timeContainer: {
    marginTop: 0,
  },
  timeRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  timeLabel: {
    fontSize: 20,
    fontWeight: "bold",
    color: COLORS.black,
    lineHeight: 25,
    letterSpacing: 0.38,
  },

  timeDisplay: {
    width: 86,
    height: 36,

    backgroundColor: "#7676801F",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 6.93,
    // textAlign: "center",
  },
  ampmContainer: {
    width: 101,
    padding: 2,
    borderRadius: 6.93,
    flexDirection: "row",
    backgroundColor: "#7676801F",
    // justifyContent: "space-between",
  },
  ampmButton: {
    width: 48.5,
    height: 32,
    borderRadius: 6.93,
    backgroundColor: "#f0f0f0",
    alignItems: "center",
    justifyContent: "center",
  },
  selectedAmPm: {
    backgroundColor: "#fff",
    boxShadow: "0 3px 8px 0 rgba(0, 0, 0, 0.1)",
    borderWidth: 0.3,
    borderColor: "#0000000A",
  },
  ampmText: {
    fontSize: 13,
    fontWeight: "500",
    color: COLORS.black,
    textAlign: "center",
  },
  selectedAmPmText: {
    color: COLORS.black,
  },
});

export default DateTimePicker;
