import React, { useState } from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import { Text } from "react-native-paper";

interface DateTimePickerProps {
  onDateTimeChange?: (date: Date) => void;
  initialDate?: Date;
}

const DateTimePicker = ({
  onDateTimeChange,
  initialDate = new Date(),
}: DateTimePickerProps) => {
  const [selectedDate, setSelectedDate] = useState(initialDate);
  const [currentMonth, setCurrentMonth] = useState(initialDate.getMonth());
  const [currentYear, setCurrentYear] = useState(initialDate.getFullYear());
  const [hours, setHours] = useState(initialDate.getHours() % 12 || 12);
  const [minutes, setMinutes] = useState(initialDate.getMinutes());
  const [isAM, setIsAM] = useState(initialDate.getHours() < 12);

  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const weekDays = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"];

  const getDaysInMonth = (month: number, year: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (month: number, year: number) => {
    return new Date(year, month, 1).getDay();
  };

  const generateCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentMonth, currentYear);
    const firstDay = getFirstDayOfMonth(currentMonth, currentYear);
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }

    return days;
  };

  const handleDateSelect = (day: number) => {
    const newDate = new Date(
      currentYear,
      currentMonth,
      day,
      isAM ? hours % 12 : (hours % 12) + 12,
      minutes
    );
    setSelectedDate(newDate);
    onDateTimeChange?.(newDate);
  };

  const handleTimeChange = () => {
    const newDate = new Date(selectedDate);
    newDate.setHours(isAM ? hours % 12 : (hours % 12) + 12);
    newDate.setMinutes(minutes);
    setSelectedDate(newDate);
    onDateTimeChange?.(newDate);
  };

  const navigateMonth = (direction: "prev" | "next") => {
    if (direction === "prev") {
      if (currentMonth === 0) {
        setCurrentMonth(11);
        setCurrentYear(currentYear - 1);
      } else {
        setCurrentMonth(currentMonth - 1);
      }
    } else {
      if (currentMonth === 11) {
        setCurrentMonth(0);
        setCurrentYear(currentYear + 1);
      } else {
        setCurrentMonth(currentMonth + 1);
      }
    }
  };

  const formatTime = () => {
    const formattedHours = hours.toString().padStart(2, "0");
    const formattedMinutes = minutes.toString().padStart(2, "0");
    return `${formattedHours}:${formattedMinutes}`;
  };

  const calendarDays = generateCalendarDays();

  return (
    <View style={styles.container}>
      <Text variant="headlineSmall" style={styles.title}>
        Select Date & Time:
      </Text>

      {/* Month Navigation */}
      <View style={styles.monthHeader}>
        <TouchableOpacity onPress={() => navigateMonth("prev")}>
          <Text style={styles.navButton}>‹</Text>
        </TouchableOpacity>

        <Text style={styles.monthYear}>
          {months[currentMonth]} {currentYear}
        </Text>

        <TouchableOpacity onPress={() => navigateMonth("next")}>
          <Text style={styles.navButton}>›</Text>
        </TouchableOpacity>
      </View>

      {/* Week Days Header */}
      <View style={styles.weekDaysContainer}>
        {weekDays.map((day) => (
          <Text key={day} style={styles.weekDay}>
            {day}
          </Text>
        ))}
      </View>

      {/* Calendar Grid */}
      <View style={styles.calendarGrid}>
        {calendarDays.map((day, index) => {
          const isSelected =
            day === selectedDate.getDate() &&
            currentMonth === selectedDate.getMonth() &&
            currentYear === selectedDate.getFullYear();

          return (
            <TouchableOpacity
              key={index}
              style={styles.dayCell}
              onPress={() => day && handleDateSelect(day)}
              disabled={!day}
            >
              <View style={[styles.dayInner, isSelected && styles.selectedDay]}>
                <Text
                  style={[styles.dayText, isSelected && styles.selectedDayText]}
                >
                  {day || ""}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Time Picker */}
      <View style={styles.timeContainer}>
        <View style={styles.timeRow}>
          <Text variant="headlineSmall" style={styles.timeLabel}>
            Time
          </Text>

          <Text style={styles.timeDisplay}>{formatTime()}</Text>

          <View style={styles.ampmContainer}>
            <TouchableOpacity
              style={[styles.ampmButton, isAM && styles.selectedAmPm]}
              onPress={() => {
                setIsAM(true);
                handleTimeChange();
              }}
            >
              <Text style={[styles.ampmText, isAM && styles.selectedAmPmText]}>
                AM
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.ampmButton, !isAM && styles.selectedAmPm]}
              onPress={() => {
                setIsAM(false);
                handleTimeChange();
              }}
            >
              <Text style={[styles.ampmText, !isAM && styles.selectedAmPmText]}>
                PM
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#fff",
    borderRadius: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000",
    marginBottom: 20,
  },
  monthHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  monthYear: {
    fontSize: 20,
    fontWeight: "600",
    color: "#2196F3",
  },
  navButton: {
    fontSize: 24,
    color: "#2196F3",
    paddingHorizontal: 15,
    paddingVertical: 5,
  },
  weekDaysContainer: {
    flexDirection: "row",
    justifyContent: "flex-start",
    marginBottom: 10,
  },
  weekDay: {
    fontSize: 14,
    color: "#999",
    fontWeight: "500",
    width: "14.28%", // 100% / 7 days = 14.28%
    textAlign: "center",
  },
  calendarGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "flex-start",
    marginBottom: 30,
  },
  dayCell: {
    width: "14.28%", // 100% / 7 days = 14.28%
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 5,
  },
  dayInner: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  selectedDay: {
    backgroundColor: "#2196F3",
    borderRadius: 20,
  },
  dayText: {
    fontSize: 16,
    color: "#2196F3",
    fontWeight: "500",
  },
  selectedDayText: {
    color: "#fff",
    fontWeight: "bold",
  },
  timeContainer: {
    marginTop: 20,
  },
  timeRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  timeLabel: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000",
  },

  timeDisplay: {
    fontSize: 32,
    fontWeight: "300",
    color: "#000",
  },
  ampmContainer: {
    flexDirection: "row",
  },
  ampmButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginHorizontal: 5,
    borderRadius: 8,
    backgroundColor: "#f0f0f0",
  },
  selectedAmPm: {
    backgroundColor: "#2196F3",
  },
  ampmText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#666",
  },
  selectedAmPmText: {
    color: "#fff",
  },
});

export default DateTimePicker;
