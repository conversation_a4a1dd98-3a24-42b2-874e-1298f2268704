import React from "react";
import { StyleSheet, View } from "react-native";
import { Icon, Text } from "react-native-paper";

const DataNotFound = ({ message = "Data Not Found" }) => {
  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
      <Text>{message}</Text>
      <Icon source="emoticon-sad-outline" size={24} color="#000" />
    </View>
  );
};

export default DataNotFound;

const styles = StyleSheet.create({});
