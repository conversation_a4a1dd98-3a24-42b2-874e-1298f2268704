import { COLORS } from "@/constants";
import React from "react";
import { StyleSheet, View } from "react-native";
import { Icon, Text } from "react-native-paper";

const DataNotFound = ({ message = "Data Not Found" }) => {
  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        gap: 10,
        backgroundColor: COLORS.white,
      }}
    >
      <Text
        style={{
          color: COLORS.gray,
          fontFamily: "Nunito-Regular",
          fontSize: 20,
        }}
      >
        {message}
      </Text>
      <Icon source="emoticon-sad-outline" size={55} color={"#ccc"} />
    </View>
  );
};

export default DataNotFound;

const styles = StyleSheet.create({});
