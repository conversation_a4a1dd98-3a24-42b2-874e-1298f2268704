import React from "react";
import { Pressable, StyleSheet, Text, View } from "react-native";

type SegmentedButtonsProps = {
  value: string;
  onValueChange: (val: string) => void;
  buttons: {
    value: string;
    label: string;
  }[];
};

const SegmentedButtons = ({
  value,
  onValueChange,
  buttons,
}: SegmentedButtonsProps) => {
  return (
    <View style={styles.container}>
      {buttons.map((button) => (
        <Pressable
          onPress={() => onValueChange(button.value)}
          key={button.value}
          style={[
            styles.button,
            button.value === value ? styles.selectedButton : null,
          ]}
        >
          <Text
            numberOfLines={1}
            style={[
              styles.buttonText,
              button.value === value ? styles.selectedButtonText : null,
            ]}
          >
            {button.label}
          </Text>
        </Pressable>
      ))}
    </View>
  );
};

export default SegmentedButtons;

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    borderRadius: 55,
    backgroundColor: "#0D66D01a",
    justifyContent: "center",
    alignItems: "center",
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 8,
    borderRadius: 55,
    justifyContent: "center",
    alignItems: "center",
  },
  selectedButton: {
    backgroundColor: "#0D66D0",
  },
  buttonText: {
    color: "#0D66D0",
    fontSize: 14,
    fontFamily: "Nunito-Medium",
  },
  selectedButtonText: {
    color: "#fff",
  },
});
