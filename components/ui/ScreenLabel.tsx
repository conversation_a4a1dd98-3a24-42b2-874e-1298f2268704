import React from "react";
import { StyleSheet, Text, View } from "react-native";

interface ScreenLabelProps {
  label: string;
  style?: any;
}

const ScreenLabel = ({ label = "Screen Label", style }: ScreenLabelProps) => {
  return (
    <View style={[styles.container, style]}>
      <Text
        style={{
          color: "##0A0A0A",
          fontFamily: "Nunito-SemiBold",
          fontSize: 20,
        }}
      >
        {label}
      </Text>
    </View>
  );
};

export default ScreenLabel;

const styles = StyleSheet.create({
  container: {},
});
