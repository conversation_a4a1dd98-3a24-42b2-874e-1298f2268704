import React from "react";
import { StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";

interface ScreenLabelProps {
  label: string;
  style?: any;
}

const ScreenLabel = ({ label = "Screen Label", style }: ScreenLabelProps) => {
  return (
    <View style={[styles.container, style]}>
      <Text variant="headlineSmall" style={{ color: "##0A0A0A" }}>
        {label}
      </Text>
    </View>
  );
};

export default ScreenLabel;

const styles = StyleSheet.create({
  container: {},
});
