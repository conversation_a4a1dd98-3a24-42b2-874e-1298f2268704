import { COLORS } from "@/constants";
import Feather from "@expo/vector-icons/Feather";
import { useMemo } from "react";
import { Pressable, ScrollView, StyleSheet, Text, View } from "react-native";
type DropDownId = "vehicle" | "school" | "route" | null;
interface Option {
  id: number | string;
  label: string;
}

interface DropDownProps {
  id: string;
  placeholder?: string;
  options: Option[];
  selectedId?: number | string | null;
  onOptionSelected: (id: number | string) => void;
  zIndex?: number;
  onOpen?: (id: DropDownId) => void;
  isOpen: boolean;
}

const DropDown = ({
  id,
  placeholder = "Select option",
  options,
  selectedId,
  onOptionSelected,
  zIndex = 1,
  onOpen = () => {},
  isOpen = false,
}: DropDownProps) => {
  const selectedOption = useMemo(() => {
    return options?.find((option) => option.id === selectedId) || null;
  }, [options, selectedId]);

  const handleOptionSelected = (option: Option) => {
    onOptionSelected(option.id);
  };

  return (
    <View className="w-full">
      <Pressable
        accessible
        onPress={() => (isOpen ? onOpen(null) : onOpen(id as DropDownId))}
        style={{ zIndex }}
        className="h-[48] flex-row items-center justify-between rounded-[14] border border-[#C7C4C4] bg-[#0D66D00A] px-[8]"
      >
        <Text
          className={`text-left`}
          style={{
            fontFamily: "Nunito-SemiBold",
            fontSize: 14,
            color: COLORS.black,
          }}
        >
          {selectedOption?.label || placeholder}
        </Text>
        <Feather
          name={isOpen ? "chevron-up" : "chevron-down"}
          size={24}
          color="#999"
        />
      </Pressable>

      {isOpen && options?.length > 0 ? (
        <View
          className="absolute left-0 right-0 top-[50] max-h-[250] overflow-hidden rounded-[14] border border-[#C7C4C4] "
          style={{ zIndex, boxShadow: "0 .5 2 0.5 rgba(0,0,0, 0.10)" }}
        >
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollViewContent}
            showsVerticalScrollIndicator={false}
          >
            {options.map((item) => (
              <Pressable
                key={item.id}
                onPress={() => handleOptionSelected(item)}
                className={`h-[48] justify-center ${
                  item.id === selectedId
                    ? "border-l-0 bg-[#0D66D0]"
                    : "border-l-0 bg-white"
                }`}
              >
                <Text
                  style={{
                    color: item.id === selectedId ? COLORS.white : COLORS.black,
                    fontFamily: "Nunito-SemiBold",
                    fontSize: 14,
                  }}
                  className="px-[8]"
                >
                  {item.label}
                </Text>
              </Pressable>
            ))}
          </ScrollView>
        </View>
      ) : (
        isOpen && (
          <View
            className="absolute left-0 right-0 top-[50] max-h-[250] overflow-hidden rounded-[4] border "
            style={{ zIndex: zIndex }}
          >
            <Pressable
              className={`h-[48] justify-center ${"border-l-0 bg-white"}`}
              onPress={() => onOpen(null)}
            >
              <Text
                style={{
                  fontFamily: "Nunito-SemiBold",
                  fontSize: 12,
                  color: COLORS.gray,
                }}
                className="px-[8] "
              >
                No options available
              </Text>
            </Pressable>
          </View>
        )
      )}
    </View>
  );
};

export default DropDown;

const styles = StyleSheet.create({
  scrollView: {
    maxHeight: 250,
    backgroundColor: "white",
  },
  scrollViewContent: {
    backgroundColor: "white",
  },
});
