import * as React from "react";
import {
  Image,
  ImageSourcePropType,
  Pressable,
  StyleSheet,
  View,
} from "react-native";
import { Surface, Text } from "react-native-paper";

type propsType = {
  media?: ImageSourcePropType;
  title: string;
  titleStyle?: {};
  description: string;
  handlePress?: () => void;
};

const ElevatedSurfaceCard = ({
  media,
  title,
  titleStyle,
  description,
  handlePress,
}: propsType) => (
  <Surface style={styles.surface} elevation={1}>
    <Pressable
      style={styles.pressableContainer}
      onPress={handlePress && handlePress}
    >
      {media && (
        <View style={styles.imageContainer}>
          <Image style={styles.image} source={media} resizeMode="contain" />
        </View>
      )}
      <Text style={[styles.title, titleStyle]} variant="titleMedium">
        {title}
      </Text>
      <Text style={styles.description}>{description}</Text>
    </Pressable>
  </Surface>
);

export default ElevatedSurfaceCard;

const styles = StyleSheet.create({
  surface: {
    borderRadius: 12,
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
  pressableContainer: {
    // backgroundColor: "red",
    padding: 20,
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
  imageContainer: {
    width: 60,
    aspectRatio: 1,
  },
  image: {
    width: "100%",
    height: "100%",
  },
  title: { paddingTop: 10 },
  description: {
    textAlign: "center",
    paddingTop: 10,
  },
});
