import React from "react";
import { StyleSheet, View } from "react-native";
import { Icon, Text, TouchableRipple } from "react-native-paper";

interface MyProfileActionButtonProps {
  icon: string;
  color: string;
  label: string;
  onPress: () => void;
}

const MyProfileActionButton = ({
  icon,
  color,
  label,
  onPress,
}: MyProfileActionButtonProps) => {
  return (
    <TouchableRipple onPress={onPress} style={{ margin: 0, width: "100%" }}>
      <View
        style={{
          // width: "100%",
          flexDirection: "row",
          justifyContent: "flex-start",
          alignItems: "flex-start",
          // backgroundColor: "#f0f4f9",
          padding: 16,
          gap: 18,
          borderRadius: 8,
        }}
      >
        <Icon source={icon} color={color} size={24} />
        <Text variant="bodyLarge" style={{ color: color }}>
          {label}
        </Text>
        {/* <Icon source="chevron-right" color={color} size={24} /> */}
      </View>
    </TouchableRipple>
  );
};

export default MyProfileActionButton;

const styles = StyleSheet.create({});
