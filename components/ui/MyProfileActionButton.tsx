import React from "react";
import { Text, View } from "react-native";
import { Icon, TouchableRipple } from "react-native-paper";

interface MyProfileActionButtonProps {
  icon: string;
  color: string;
  label: string;
  onPress: () => void;
}

const MyProfileActionButton = ({
  icon,
  color,
  label,
  onPress,
}: MyProfileActionButtonProps) => {
  return (
    <TouchableRipple
      onPress={onPress}
      style={{ margin: 0, width: "100%", borderRadius: 16 }}
      rippleColor={"rgba(0, 0, 0, 0.03)"}
    >
      <View
        style={{
          flexDirection: "row",
          justifyContent: "flex-start",
          alignItems: "flex-start",
          paddingVertical: 16,
          gap: 18,
        }}
      >
        <Icon source={icon} color={color} size={24} />
        <Text
          style={{ color: color, fontFamily: "Nunito-Medium", fontSize: 18 }}
        >
          {label}
        </Text>
      </View>
    </TouchableRipple>
  );
};

export default MyProfileActionButton;
