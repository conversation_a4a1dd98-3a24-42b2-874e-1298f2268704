{"expo": {"name": "COPDInsightRN", "slug": "COPDInsightRN", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "copdinsightrn", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.nepesmo.copdpredict"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "237d457b-fafc-4d09-abc7-8c6203aad9d4"}}}}